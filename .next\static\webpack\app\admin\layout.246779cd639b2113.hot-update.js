"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item\n    async create (item) {\n        const { data, error } = await supabase.from('menu_items').insert(item).select(\"\\n        *,\\n        category:categories(*)\\n      \").single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item\n    async update (id, updates) {\n        console.log('Updating menu item in database:', id, updates);\n        // First check if the item exists\n        const { data: existingItem, error: checkError } = await supabase.from('menu_items').select('*').eq('id', id).single();\n        console.log('Existing item check:', {\n            existingItem,\n            checkError\n        });\n        if (checkError || !existingItem) {\n            throw new Error(\"Menu item with ID \".concat(id, \" not found: \").concat((checkError === null || checkError === void 0 ? void 0 : checkError.message) || 'No data returned'));\n        }\n        // Try the update without .single() first to see what happens\n        const { data: updateResult, error: updateError } = await supabase.from('menu_items').update(updates).eq('id', id).select(\"\\n        *,\\n        category:categories(*)\\n      \");\n        console.log('Update result:', {\n            updateResult,\n            updateError,\n            rowCount: updateResult === null || updateResult === void 0 ? void 0 : updateResult.length\n        });\n        if (updateError) {\n            console.error('Update error:', updateError);\n            throw updateError;\n        }\n        if (!updateResult || updateResult.length === 0) {\n            throw new Error('No rows were updated - this might be a permissions issue');\n        }\n        if (updateResult.length > 1) {\n            console.warn('Multiple rows updated, returning first one');\n        }\n        return updateResult[0];\n    },\n    // Delete menu item\n    async delete (id) {\n        const { error } = await supabase.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});