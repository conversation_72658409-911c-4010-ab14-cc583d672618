import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { User } from './auth';

// Auth utilities for server components
export const createServerAuth = () => {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );
  
  return {
    // Get current session on server
    async getSession() {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      return session;
    },

    // Get current user on server
    async getCurrentUser(): Promise<User | null> {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) return null;

      const metadata = user.user_metadata || {};
      return {
        id: user.id,
        email: user.email!,
        full_name: metadata.full_name,
        role: metadata.role || 'user',
        is_admin: metadata.is_admin || false,
        last_login: metadata.last_login,
      };
    },
  };
};
