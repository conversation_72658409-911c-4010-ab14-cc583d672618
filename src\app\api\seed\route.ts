import { NextRequest, NextResponse } from 'next/server';
import { seedDatabase, clearDatabase } from '@/lib/seed-database';

// POST /api/seed - Seed the database with sample data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'clear') {
      await clearDatabase();
      return NextResponse.json({ message: 'Database cleared successfully' });
    } else {
      const result = await seedDatabase();
      return NextResponse.json({ message: 'Database seeded successfully', result });
    }
  } catch (error) {
    console.error('Error in seed API:', error);
    return NextResponse.json(
      { error: 'Failed to seed database', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET /api/seed - Check database status
export async function GET() {
  try {
    return NextResponse.json({ 
      message: 'Seed API is available. Use POST with {"action": "seed"} to seed database or {"action": "clear"} to clear it.' 
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to check seed status' },
      { status: 500 }
    );
  }
}
