'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { Heart, Users, Award, Coffee } from 'lucide-react';

export function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const features = [
    {
      icon: Heart,
      title: "Authentic Heritage",
      description: "Traditional recipes passed down through generations, preserving the true essence of Ethiopian cuisine."
    },
    {
      icon: Users,
      title: "Family Tradition",
      description: "Founded by the Chachi family, bringing decades of culinary expertise and Ethiopian hospitality."
    },
    {
      icon: Award,
      title: "Premium Quality",
      description: "Only the finest ingredients sourced locally and imported spices to ensure authentic flavors."
    },
    {
      icon: Coffee,
      title: "Coffee Ceremony",
      description: "Experience the traditional Ethiopian coffee ceremony, a sacred ritual of hospitality and community."
    }
  ];

  return (
    <section id="about" className="py-20 lg:py-32 bg-muted/30" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={isInView ? { scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-block mb-6"
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto">
                <div className="w-6 h-6 border-2 border-primary-foreground rounded-full"></div>
              </div>
            </motion.div>
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-foreground mb-6">
              Our Story
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Nestled in the vibrant heart of Addis Ababa, Chachi's Restaurant has been a beacon of 
              authentic Ethiopian cuisine and warm hospitality for over two decades.
            </p>
          </motion.div>

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-20">
            {/* Story Text */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="space-y-6"
            >
              <h3 className="text-2xl lg:text-3xl font-serif font-semibold text-foreground">
                A Legacy of <span className="text-primary">Flavor</span> and <span className="text-accent">Tradition</span>
              </h3>
              
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  Founded by Grandmother Chachi in 1998, our restaurant began as a small family kitchen 
                  where neighbors would gather to share meals and stories. What started as a humble 
                  beginning has blossomed into one of Addis Ababa's most beloved dining destinations.
                </p>
                
                <p>
                  Every dish we serve carries the soul of Ethiopian culture - from our hand-ground 
                  berbere spice blend to our traditional injera bread, baked fresh daily using 
                  ancient techniques. We believe that food is more than sustenance; it's a bridge 
                  that connects hearts and creates lasting memories.
                </p>
                
                <p>
                  Today, under the guidance of the third generation of the Chachi family, we continue 
                  to honor our heritage while embracing the future, creating an atmosphere where 
                  tradition and elegance dance together in perfect harmony.
                </p>
              </div>
            </motion.div>

            {/* Image Placeholder with Animation */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="relative"
            >
              <div className="aspect-[4/3] bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl overflow-hidden relative">
                {/* Placeholder for restaurant image */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <Coffee size={32} className="text-primary" />
                    </div>
                    <p className="text-muted-foreground font-medium">Restaurant Interior</p>
                  </div>
                </div>
                
                {/* Decorative Elements */}
                <div className="absolute top-4 right-4 w-8 h-8 border-2 border-primary/50 rounded-full"></div>
                <div className="absolute bottom-4 left-4 w-6 h-6 bg-accent/30 rounded-full"></div>
              </div>
            </motion.div>
          </div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors duration-300">
                  <feature.icon size={32} className="text-primary" />
                </div>
                <h4 className="text-lg font-semibold text-foreground mb-2">
                  {feature.title}
                </h4>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
