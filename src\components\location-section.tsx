'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { MapPin, Clock, Phone, Mail } from 'lucide-react';

export function LocationSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const contactInfo = [
    {
      icon: MapPin,
      title: "Location",
      details: ["Bole Road, Near Edna Mall", "Addis Ababa, Ethiopia"],
      color: "text-primary"
    },
    {
      icon: Clock,
      title: "Hours",
      details: ["Mon - Sun: 11:00 AM - 11:00 PM", "Coffee Ceremony: 3:00 PM - 6:00 PM"],
      color: "text-accent"
    },
    {
      icon: Phone,
      title: "Phone",
      details: ["+251 11 123 4567", "+251 91 234 5678"],
      color: "text-secondary"
    },
    {
      icon: Mail,
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"],
      color: "text-primary"
    }
  ];

  return (
    <section id="location" className="py-20 lg:py-32 bg-muted/30" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={isInView ? { scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-block mb-6"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-accent to-secondary rounded-full flex items-center justify-center mx-auto">
                <MapPin size={32} className="text-white" />
              </div>
            </motion.div>
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-foreground mb-6">
              Visit <span className="text-primary">Chachi's</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Located in the heart of Addis Ababa, we're easily accessible and ready to welcome you 
              with traditional Ethiopian hospitality.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="space-y-8"
            >
              <h3 className="text-2xl lg:text-3xl font-serif font-semibold text-foreground mb-8">
                Get in <span className="text-primary">Touch</span>
              </h3>

              {contactInfo.map((info, index) => (
                <motion.div
                  key={info.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  className="flex items-start gap-4 group"
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${info.color} bg-current/10 group-hover:bg-current/20 transition-colors duration-300`}>
                    <info.icon size={24} className={info.color} />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-foreground mb-2">
                      {info.title}
                    </h4>
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-muted-foreground">
                        {detail}
                      </p>
                    ))}
                  </div>
                </motion.div>
              ))}

              {/* Special Note */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="bg-primary/10 rounded-2xl p-6 border border-primary/20"
              >
                <h4 className="text-lg font-semibold text-primary mb-2">
                  Reservation Recommended
                </h4>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  While walk-ins are welcome, we recommend making a reservation to ensure 
                  the best dining experience, especially for our traditional coffee ceremony.
                </p>
              </motion.div>
            </motion.div>

            {/* Map Placeholder */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="relative"
            >
              <div className="aspect-[4/3] bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl overflow-hidden relative border border-border">
                {/* Map Placeholder */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <MapPin size={32} className="text-primary" />
                    </div>
                    <p className="text-muted-foreground font-medium mb-2">Interactive Map</p>
                    <p className="text-sm text-muted-foreground">
                      Bole Road, Near Edna Mall<br />
                      Addis Ababa, Ethiopia
                    </p>
                  </div>
                </div>
                
                {/* Decorative Elements */}
                <div className="absolute top-4 left-4 w-3 h-3 bg-accent rounded-full animate-pulse"></div>
                <div className="absolute top-8 right-8 w-2 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <div className="absolute bottom-6 left-8 w-4 h-4 border-2 border-secondary/50 rounded-full"></div>
                <div className="absolute bottom-4 right-4 w-6 h-6 bg-primary/20 rounded-full"></div>
              </div>

              {/* Directions Button */}
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="absolute bottom-6 left-6 bg-primary text-primary-foreground px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Get Directions
              </motion.button>
            </motion.div>
          </div>

          {/* Additional Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.9 }}
            className="mt-16 text-center"
          >
            <div className="bg-background rounded-2xl p-8 border border-border shadow-sm">
              <h4 className="text-xl font-semibold text-foreground mb-4">
                Experience Ethiopian Culture
              </h4>
              <p className="text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                Beyond exceptional dining, Chachi's offers cultural experiences including traditional 
                coffee ceremonies, live Ethiopian music on weekends, and cooking classes for those 
                interested in learning the art of Ethiopian cuisine.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
