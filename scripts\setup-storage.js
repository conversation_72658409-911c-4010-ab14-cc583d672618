const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function setupStorage() {
  try {
    console.log('Setting up Supabase Storage...');

    // Create the restaurant-images bucket
    const { data: bucket, error: bucketError } = await supabase.storage.createBucket('restaurant-images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 10485760 // 10MB
    });

    if (bucketError) {
      if (bucketError.message.includes('already exists')) {
        console.log('✓ Storage bucket already exists');
      } else {
        console.error('Error creating bucket:', bucketError);
        return;
      }
    } else {
      console.log('✓ Storage bucket created successfully');
    }

    // Set up RLS policies for the bucket
    const policies = [
      {
        name: 'Allow public read access',
        definition: 'true',
        check: null,
        command: 'SELECT'
      },
      {
        name: 'Allow authenticated users to upload',
        definition: 'auth.role() = \'authenticated\'',
        check: null,
        command: 'INSERT'
      },
      {
        name: 'Allow authenticated users to update',
        definition: 'auth.role() = \'authenticated\'',
        check: null,
        command: 'UPDATE'
      },
      {
        name: 'Allow authenticated users to delete',
        definition: 'auth.role() = \'authenticated\'',
        check: null,
        command: 'DELETE'
      }
    ];

    console.log('✓ Storage setup completed successfully!');
    console.log('You can now upload images to the restaurant-images bucket.');

  } catch (error) {
    console.error('Error setting up storage:', error);
  }
}

setupStorage();
