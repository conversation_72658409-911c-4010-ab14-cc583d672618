"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"084e6a3f7706\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcSm9zc3kgUmVzdGF1cmFudFxcY2hhY2hpc1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDg0ZTZhM2Y3NzA2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\n// Create Supabase client for public operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Create Supabase admin client for server-side operations (bypasses RLS)\n// This should only be used on the server side\nfunction createAdminClient() {\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Only create admin client on server side\nconst supabaseAdmin =  false ? 0 : null;\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item (using admin client)\n    async create (item) {\n        if (!supabaseAdmin) {\n            throw new Error('Admin operations are only available on the server side');\n        }\n        const { data, error } = await supabaseAdmin.from('menu_items').insert(item).select(\"\\n        *,\\n        category:categories(*)\\n      \").single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item (using admin client)\n    async update (id, updates) {\n        console.log('Updating menu item in database:', id, updates);\n        // First check if the item exists using admin client\n        const { data: existingItem, error: checkError } = await supabaseAdmin.from('menu_items').select('*').eq('id', id).single();\n        console.log('Existing item check:', {\n            existingItem,\n            checkError\n        });\n        if (checkError || !existingItem) {\n            throw new Error(\"Menu item with ID \".concat(id, \" not found: \").concat((checkError === null || checkError === void 0 ? void 0 : checkError.message) || 'No data returned'));\n        }\n        // Update using admin client\n        const { data: updateResult, error: updateError } = await supabaseAdmin.from('menu_items').update(updates).eq('id', id).select(\"\\n        *,\\n        category:categories(*)\\n      \");\n        console.log('Update result:', {\n            updateResult,\n            updateError,\n            rowCount: updateResult === null || updateResult === void 0 ? void 0 : updateResult.length\n        });\n        if (updateError) {\n            console.error('Update error:', updateError);\n            throw updateError;\n        }\n        if (!updateResult || updateResult.length === 0) {\n            throw new Error('No rows were updated - this might be a permissions issue');\n        }\n        if (updateResult.length > 1) {\n            console.warn('Multiple rows updated, returning first one');\n        }\n        return updateResult[0];\n    },\n    // Delete menu item (using admin client)\n    async delete (id) {\n        const { error } = await supabaseAdmin.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBcUQ7QUFFckQseUJBQXlCO0FBQ3pCLE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSwrQ0FBK0M7QUFDeEMsTUFBTUssV0FBV1AsbUVBQVlBLENBQUNDLGFBQWFJLGlCQUFpQjtBQUVuRSx5RUFBeUU7QUFDekUsOENBQThDO0FBQzlDLFNBQVNHO0lBQ1AsTUFBTUMscUJBQXFCUCxPQUFPQSxDQUFDQyxHQUFHLENBQUNPLHlCQUF5QjtJQUNoRSxJQUFJLENBQUNELG9CQUFvQjtRQUN2QixNQUFNLElBQUlFLE1BQU07SUFDbEI7SUFFQSxPQUFPWCxtRUFBWUEsQ0FBQ0MsYUFBYVEsb0JBQW9CO1FBQ25ERyxNQUFNO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7QUFDRjtBQUVBLDBDQUEwQztBQUNuQyxNQUFNQyxnQkFBZ0IsTUFBNkIsR0FBR1AsQ0FBbUJBLEdBQUcsS0FBSztBQTREeEYsK0JBQStCO0FBQ3hCLE1BQU1RLGNBQWM7SUFDekIsNEJBQTRCO0lBQzVCLE1BQU1DO1FBQ0osTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1aLFNBQzNCYSxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxhQUFhLE1BQ2hCQyxLQUFLLENBQUMsaUJBQWlCO1lBQUVDLFdBQVc7UUFBSztRQUU1QyxJQUFJTCxPQUFPLE1BQU1BO1FBQ2pCLE9BQU9ELFFBQVEsRUFBRTtJQUNuQjtJQUVBLHFCQUFxQjtJQUNyQixNQUFNTyxTQUFRQyxFQUFVO1FBQ3RCLE1BQU0sRUFBRVIsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTUksSUFDVEMsTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLHNCQUFzQjtJQUN0QixNQUFNVSxRQUFPQyxRQUE0RDtRQUN2RSxNQUFNLEVBQUVYLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVosU0FDM0JhLElBQUksQ0FBQyxjQUNMVSxNQUFNLENBQUNELFVBQ1BSLE1BQU0sR0FDTk0sTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLGtCQUFrQjtJQUNsQixNQUFNYSxRQUFPTCxFQUFVLEVBQUVNLE9BQTBCO1FBQ2pELE1BQU0sRUFBRWQsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGNBQ0xXLE1BQU0sQ0FBQ0MsU0FDUFYsRUFBRSxDQUFDLE1BQU1JLElBQ1RMLE1BQU0sR0FDTk0sTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLGtCQUFrQjtJQUNsQixNQUFNZSxRQUFPUCxFQUFVO1FBQ3JCLE1BQU0sRUFBRVAsS0FBSyxFQUFFLEdBQUcsTUFBTVosU0FDckJhLElBQUksQ0FBQyxjQUNMYSxNQUFNLEdBQ05YLEVBQUUsQ0FBQyxNQUFNSTtRQUVaLElBQUlQLE9BQU8sTUFBTUE7SUFDbkI7QUFDRixFQUFFO0FBRUYsK0JBQStCO0FBQ3hCLE1BQU1lLFVBQVU7SUFDckIsK0NBQStDO0lBQy9DLE1BQU1qQjtRQUNKLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBRSx3REFJUkMsRUFBRSxDQUFDLGdCQUFnQixNQUNuQkMsS0FBSyxDQUFDLGlCQUFpQjtZQUFFQyxXQUFXO1FBQUs7UUFFNUMsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRCxRQUFRLEVBQUU7SUFDbkI7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTWlCLGVBQWNDLFVBQWtCO1FBQ3BDLE1BQU0sRUFBRWxCLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVosU0FDM0JhLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUUsd0RBSVJDLEVBQUUsQ0FBQyxlQUFlYyxZQUNsQmQsRUFBRSxDQUFDLGdCQUFnQixNQUNuQkMsS0FBSyxDQUFDLGlCQUFpQjtZQUFFQyxXQUFXO1FBQUs7UUFFNUMsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRCxRQUFRLEVBQUU7SUFDbkI7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTW1CO1FBQ0osTUFBTSxFQUFFbkIsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBRSx3REFJUkMsRUFBRSxDQUFDLGVBQWUsTUFDbEJBLEVBQUUsQ0FBQyxnQkFBZ0IsTUFDbkJDLEtBQUssQ0FBQyxpQkFBaUI7WUFBRUMsV0FBVztRQUFLO1FBRTVDLElBQUlMLE9BQU8sTUFBTUE7UUFDakIsT0FBT0QsUUFBUSxFQUFFO0lBQ25CO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1PLFNBQVFDLEVBQVU7UUFDdEIsTUFBTSxFQUFFUixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1aLFNBQzNCYSxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFFLHdEQUlSQyxFQUFFLENBQUMsTUFBTUksSUFDVEMsTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLDRDQUE0QztJQUM1QyxNQUFNVSxRQUFPVSxJQUFxRTtRQUNoRixJQUFJLENBQUN2QixlQUFlO1lBQ2xCLE1BQU0sSUFBSUosTUFBTTtRQUNsQjtRQUVBLE1BQU0sRUFBRU8sSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSixjQUMzQkssSUFBSSxDQUFDLGNBQ0xVLE1BQU0sQ0FBQ1EsTUFDUGpCLE1BQU0sQ0FBRSx3REFJUk0sTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLHdDQUF3QztJQUN4QyxNQUFNYSxRQUFPTCxFQUFVLEVBQUVNLE9BQTBCO1FBQ2pETyxRQUFRQyxHQUFHLENBQUMsbUNBQW1DZCxJQUFJTTtRQUVuRCxvREFBb0Q7UUFDcEQsTUFBTSxFQUFFZCxNQUFNdUIsWUFBWSxFQUFFdEIsT0FBT3VCLFVBQVUsRUFBRSxHQUFHLE1BQU0zQixjQUNyREssSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTUksSUFDVEMsTUFBTTtRQUVUWSxRQUFRQyxHQUFHLENBQUMsd0JBQXdCO1lBQUVDO1lBQWNDO1FBQVc7UUFFL0QsSUFBSUEsY0FBYyxDQUFDRCxjQUFjO1lBQy9CLE1BQU0sSUFBSTlCLE1BQU0scUJBQXNDK0IsT0FBakJoQixJQUFHLGdCQUF3RCxPQUExQ2dCLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWUMsT0FBTyxLQUFJO1FBQy9FO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU0sRUFBRXpCLE1BQU0wQixZQUFZLEVBQUV6QixPQUFPMEIsV0FBVyxFQUFFLEdBQUcsTUFBTTlCLGNBQ3RESyxJQUFJLENBQUMsY0FDTFcsTUFBTSxDQUFDQyxTQUNQVixFQUFFLENBQUMsTUFBTUksSUFDVEwsTUFBTSxDQUFFO1FBS1hrQixRQUFRQyxHQUFHLENBQUMsa0JBQWtCO1lBQUVJO1lBQWNDO1lBQWFDLFFBQVEsRUFBRUYseUJBQUFBLG1DQUFBQSxhQUFjRyxNQUFNO1FBQUM7UUFFMUYsSUFBSUYsYUFBYTtZQUNmTixRQUFRcEIsS0FBSyxDQUFDLGlCQUFpQjBCO1lBQy9CLE1BQU1BO1FBQ1I7UUFFQSxJQUFJLENBQUNELGdCQUFnQkEsYUFBYUcsTUFBTSxLQUFLLEdBQUc7WUFDOUMsTUFBTSxJQUFJcEMsTUFBTTtRQUNsQjtRQUVBLElBQUlpQyxhQUFhRyxNQUFNLEdBQUcsR0FBRztZQUMzQlIsUUFBUVMsSUFBSSxDQUFDO1FBQ2Y7UUFFQSxPQUFPSixZQUFZLENBQUMsRUFBRTtJQUN4QjtJQUVBLHdDQUF3QztJQUN4QyxNQUFNWCxRQUFPUCxFQUFVO1FBQ3JCLE1BQU0sRUFBRVAsS0FBSyxFQUFFLEdBQUcsTUFBTUosY0FDckJLLElBQUksQ0FBQyxjQUNMYSxNQUFNLEdBQ05YLEVBQUUsQ0FBQyxNQUFNSTtRQUVaLElBQUlQLE9BQU8sTUFBTUE7SUFDbkI7QUFDRixFQUFFO0FBRUYsb0NBQW9DO0FBQzdCLE1BQU04QixnQkFBZ0I7SUFDM0IsNkJBQTZCO0lBQzdCLE1BQU1DO1FBQ0osTUFBTSxFQUFFaEMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLG1CQUNMQyxNQUFNLENBQUMsS0FDUDhCLEtBQUssQ0FBQyxHQUNOeEIsTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLGdDQUFnQztJQUNoQyxNQUFNa0MsWUFBV3BCLE9BQWdDO1FBQy9DLE1BQU0sRUFBRWQsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLG1CQUNMVyxNQUFNLENBQUNDLFNBQ1BYLE1BQU0sR0FDTk0sTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtBQUNGLEVBQUU7QUFFRixnQ0FBZ0M7QUFDekIsTUFBTW1DLFdBQVc7SUFDdEIsc0JBQXNCO0lBQ3RCLE1BQU1wQztRQUNKLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGVBQ0xDLE1BQU0sQ0FBQyxLQUNQRSxLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU07UUFFMUMsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRCxRQUFRLEVBQUU7SUFDbkI7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTW9DLFlBQVdDLEtBQWE7UUFDNUIsTUFBTSxFQUFFckMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGVBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsU0FBU2lDLE9BQ1o1QixNQUFNO1FBRVQsSUFBSVIsT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1VLFFBQU80QixJQUF5RDtRQUNwRSxNQUFNLEVBQUV0QyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1aLFNBQzNCYSxJQUFJLENBQUMsZUFDTFUsTUFBTSxDQUFDMEIsTUFDUG5DLE1BQU0sR0FDTk0sTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLG9CQUFvQjtJQUNwQixNQUFNYSxRQUFPTCxFQUFVLEVBQUVNLE9BQTJCO1FBQ2xELE1BQU0sRUFBRWQsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixTQUMzQmEsSUFBSSxDQUFDLGVBQ0xXLE1BQU0sQ0FBQ0MsU0FDUFYsRUFBRSxDQUFDLE1BQU1JLElBQ1RMLE1BQU0sR0FDTk0sTUFBTTtRQUVULElBQUlSLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcS3JhaW1hdGljXFxEZXNrdG9wXFxKb3NzeSBSZXN0YXVyYW50XFxjaGFjaGlzXFxzcmNcXGxpYlxcc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLy8gU3VwYWJhc2UgY29uZmlndXJhdGlvblxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhO1xuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhO1xuXG4vLyBDcmVhdGUgU3VwYWJhc2UgY2xpZW50IGZvciBwdWJsaWMgb3BlcmF0aW9uc1xuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXkpO1xuXG4vLyBDcmVhdGUgU3VwYWJhc2UgYWRtaW4gY2xpZW50IGZvciBzZXJ2ZXItc2lkZSBvcGVyYXRpb25zIChieXBhc3NlcyBSTFMpXG4vLyBUaGlzIHNob3VsZCBvbmx5IGJlIHVzZWQgb24gdGhlIHNlcnZlciBzaWRlXG5mdW5jdGlvbiBjcmVhdGVBZG1pbkNsaWVudCgpIHtcbiAgY29uc3Qgc3VwYWJhc2VTZXJ2aWNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWTtcbiAgaWYgKCFzdXBhYmFzZVNlcnZpY2VLZXkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1NVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkgaXMgcmVxdWlyZWQgZm9yIGFkbWluIG9wZXJhdGlvbnMnKTtcbiAgfVxuXG4gIHJldHVybiBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlU2VydmljZUtleSwge1xuICAgIGF1dGg6IHtcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGZhbHNlLFxuICAgICAgcGVyc2lzdFNlc3Npb246IGZhbHNlXG4gICAgfVxuICB9KTtcbn1cblxuLy8gT25seSBjcmVhdGUgYWRtaW4gY2xpZW50IG9uIHNlcnZlciBzaWRlXG5leHBvcnQgY29uc3Qgc3VwYWJhc2VBZG1pbiA9IHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnID8gY3JlYXRlQWRtaW5DbGllbnQoKSA6IG51bGw7XG5cbi8vIERhdGFiYXNlIHR5cGVzXG5leHBvcnQgaW50ZXJmYWNlIENhdGVnb3J5IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbj86IHN0cmluZztcbiAgaWNvbj86IHN0cmluZztcbiAgZGlzcGxheV9vcmRlcjogbnVtYmVyO1xuICBpc19hY3RpdmU6IGJvb2xlYW47XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIE1lbnVJdGVtIHtcbiAgaWQ6IHN0cmluZztcbiAgY2F0ZWdvcnlfaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbj86IHN0cmluZztcbiAgcHJpY2U6IG51bWJlcjtcbiAgY3VycmVuY3k6IHN0cmluZztcbiAgaW1hZ2VfdXJsPzogc3RyaW5nO1xuICBpc19hdmFpbGFibGU6IGJvb2xlYW47XG4gIGlzX2ZlYXR1cmVkOiBib29sZWFuO1xuICBzcGljZV9sZXZlbDogbnVtYmVyO1xuICBkaWV0YXJ5X2luZm8/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xuICBpbmdyZWRpZW50cz86IHN0cmluZ1tdO1xuICBhbGxlcmdlbnM/OiBzdHJpbmdbXTtcbiAgcHJlcGFyYXRpb25fdGltZT86IG51bWJlcjtcbiAgZGlzcGxheV9vcmRlcjogbnVtYmVyO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbiAgY2F0ZWdvcnk/OiBDYXRlZ29yeTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBZG1pblVzZXIge1xuICBpZDogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBmdWxsX25hbWU6IHN0cmluZztcbiAgcm9sZTogJ2l0X2FkbWluJyB8ICdyZXN0YXVyYW50X2FkbWluJztcbiAgaXNfYWN0aXZlOiBib29sZWFuO1xuICBsYXN0X2xvZ2luPzogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSZXN0YXVyYW50SW5mbyB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGFkZHJlc3M/OiBzdHJpbmc7XG4gIHBob25lPzogc3RyaW5nO1xuICBlbWFpbD86IHN0cmluZztcbiAgb3BlbmluZ19ob3Vycz86IFJlY29yZDxzdHJpbmcsIGFueT47XG4gIHNvY2lhbF9tZWRpYT86IFJlY29yZDxzdHJpbmcsIGFueT47XG4gIHNldHRpbmdzPzogUmVjb3JkPHN0cmluZywgYW55PjtcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xuICB1cGRhdGVkX2J5Pzogc3RyaW5nO1xufVxuXG4vLyBBUEkgZnVuY3Rpb25zIGZvciBjYXRlZ29yaWVzXG5leHBvcnQgY29uc3QgY2F0ZWdvcnlBUEkgPSB7XG4gIC8vIEdldCBhbGwgYWN0aXZlIGNhdGVnb3JpZXNcbiAgYXN5bmMgZ2V0QWxsKCk6IFByb21pc2U8Q2F0ZWdvcnlbXT4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgnaXNfYWN0aXZlJywgdHJ1ZSlcbiAgICAgIC5vcmRlcignZGlzcGxheV9vcmRlcicsIHsgYXNjZW5kaW5nOiB0cnVlIH0pO1xuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvcjtcbiAgICByZXR1cm4gZGF0YSB8fCBbXTtcbiAgfSxcblxuICAvLyBHZXQgY2F0ZWdvcnkgYnkgSURcbiAgYXN5bmMgZ2V0QnlJZChpZDogc3RyaW5nKTogUHJvbWlzZTxDYXRlZ29yeSB8IG51bGw+IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NhdGVnb3JpZXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhO1xuICB9LFxuXG4gIC8vIENyZWF0ZSBuZXcgY2F0ZWdvcnlcbiAgYXN5bmMgY3JlYXRlKGNhdGVnb3J5OiBPbWl0PENhdGVnb3J5LCAnaWQnIHwgJ2NyZWF0ZWRfYXQnIHwgJ3VwZGF0ZWRfYXQnPik6IFByb21pc2U8Q2F0ZWdvcnk+IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NhdGVnb3JpZXMnKVxuICAgICAgLmluc2VydChjYXRlZ29yeSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvcjtcbiAgICByZXR1cm4gZGF0YTtcbiAgfSxcblxuICAvLyBVcGRhdGUgY2F0ZWdvcnlcbiAgYXN5bmMgdXBkYXRlKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8Q2F0ZWdvcnk+KTogUHJvbWlzZTxDYXRlZ29yeT4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2F0ZWdvcmllcycpXG4gICAgICAudXBkYXRlKHVwZGF0ZXMpXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH0sXG5cbiAgLy8gRGVsZXRlIGNhdGVnb3J5XG4gIGFzeW5jIGRlbGV0ZShpZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjYXRlZ29yaWVzJylcbiAgICAgIC5kZWxldGUoKVxuICAgICAgLmVxKCdpZCcsIGlkKTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gIH1cbn07XG5cbi8vIEFQSSBmdW5jdGlvbnMgZm9yIG1lbnUgaXRlbXNcbmV4cG9ydCBjb25zdCBtZW51QVBJID0ge1xuICAvLyBHZXQgYWxsIGF2YWlsYWJsZSBtZW51IGl0ZW1zIHdpdGggY2F0ZWdvcmllc1xuICBhc3luYyBnZXRBbGwoKTogUHJvbWlzZTxNZW51SXRlbVtdPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdtZW51X2l0ZW1zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpc19hdmFpbGFibGUnLCB0cnVlKVxuICAgICAgLm9yZGVyKCdkaXNwbGF5X29yZGVyJywgeyBhc2NlbmRpbmc6IHRydWUgfSk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhIHx8IFtdO1xuICB9LFxuXG4gIC8vIEdldCBtZW51IGl0ZW1zIGJ5IGNhdGVnb3J5XG4gIGFzeW5jIGdldEJ5Q2F0ZWdvcnkoY2F0ZWdvcnlJZDogc3RyaW5nKTogUHJvbWlzZTxNZW51SXRlbVtdPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdtZW51X2l0ZW1zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdjYXRlZ29yeV9pZCcsIGNhdGVnb3J5SWQpXG4gICAgICAuZXEoJ2lzX2F2YWlsYWJsZScsIHRydWUpXG4gICAgICAub3JkZXIoJ2Rpc3BsYXlfb3JkZXInLCB7IGFzY2VuZGluZzogdHJ1ZSB9KTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgcmV0dXJuIGRhdGEgfHwgW107XG4gIH0sXG5cbiAgLy8gR2V0IGZlYXR1cmVkIG1lbnUgaXRlbXNcbiAgYXN5bmMgZ2V0RmVhdHVyZWQoKTogUHJvbWlzZTxNZW51SXRlbVtdPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdtZW51X2l0ZW1zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpc19mZWF0dXJlZCcsIHRydWUpXG4gICAgICAuZXEoJ2lzX2F2YWlsYWJsZScsIHRydWUpXG4gICAgICAub3JkZXIoJ2Rpc3BsYXlfb3JkZXInLCB7IGFzY2VuZGluZzogdHJ1ZSB9KTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgcmV0dXJuIGRhdGEgfHwgW107XG4gIH0sXG5cbiAgLy8gR2V0IG1lbnUgaXRlbSBieSBJRFxuICBhc3luYyBnZXRCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPE1lbnVJdGVtIHwgbnVsbD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnbWVudV9pdGVtcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgY2F0ZWdvcnk6Y2F0ZWdvcmllcygqKVxuICAgICAgYClcbiAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH0sXG5cbiAgLy8gQ3JlYXRlIG5ldyBtZW51IGl0ZW0gKHVzaW5nIGFkbWluIGNsaWVudClcbiAgYXN5bmMgY3JlYXRlKGl0ZW06IE9taXQ8TWVudUl0ZW0sICdpZCcgfCAnY3JlYXRlZF9hdCcgfCAndXBkYXRlZF9hdCcgfCAnY2F0ZWdvcnknPik6IFByb21pc2U8TWVudUl0ZW0+IHtcbiAgICBpZiAoIXN1cGFiYXNlQWRtaW4pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQWRtaW4gb3BlcmF0aW9ucyBhcmUgb25seSBhdmFpbGFibGUgb24gdGhlIHNlcnZlciBzaWRlJyk7XG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ21lbnVfaXRlbXMnKVxuICAgICAgLmluc2VydChpdGVtKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGNhdGVnb3J5OmNhdGVnb3JpZXMoKilcbiAgICAgIGApXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhO1xuICB9LFxuXG4gIC8vIFVwZGF0ZSBtZW51IGl0ZW0gKHVzaW5nIGFkbWluIGNsaWVudClcbiAgYXN5bmMgdXBkYXRlKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8TWVudUl0ZW0+KTogUHJvbWlzZTxNZW51SXRlbT4ge1xuICAgIGNvbnNvbGUubG9nKCdVcGRhdGluZyBtZW51IGl0ZW0gaW4gZGF0YWJhc2U6JywgaWQsIHVwZGF0ZXMpO1xuXG4gICAgLy8gRmlyc3QgY2hlY2sgaWYgdGhlIGl0ZW0gZXhpc3RzIHVzaW5nIGFkbWluIGNsaWVudFxuICAgIGNvbnN0IHsgZGF0YTogZXhpc3RpbmdJdGVtLCBlcnJvcjogY2hlY2tFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ21lbnVfaXRlbXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBjb25zb2xlLmxvZygnRXhpc3RpbmcgaXRlbSBjaGVjazonLCB7IGV4aXN0aW5nSXRlbSwgY2hlY2tFcnJvciB9KTtcblxuICAgIGlmIChjaGVja0Vycm9yIHx8ICFleGlzdGluZ0l0ZW0pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgTWVudSBpdGVtIHdpdGggSUQgJHtpZH0gbm90IGZvdW5kOiAke2NoZWNrRXJyb3I/Lm1lc3NhZ2UgfHwgJ05vIGRhdGEgcmV0dXJuZWQnfWApO1xuICAgIH1cblxuICAgIC8vIFVwZGF0ZSB1c2luZyBhZG1pbiBjbGllbnRcbiAgICBjb25zdCB7IGRhdGE6IHVwZGF0ZVJlc3VsdCwgZXJyb3I6IHVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAuZnJvbSgnbWVudV9pdGVtcycpXG4gICAgICAudXBkYXRlKHVwZGF0ZXMpXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgY2F0ZWdvcnk6Y2F0ZWdvcmllcygqKVxuICAgICAgYCk7XG5cbiAgICBjb25zb2xlLmxvZygnVXBkYXRlIHJlc3VsdDonLCB7IHVwZGF0ZVJlc3VsdCwgdXBkYXRlRXJyb3IsIHJvd0NvdW50OiB1cGRhdGVSZXN1bHQ/Lmxlbmd0aCB9KTtcblxuICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIGVycm9yOicsIHVwZGF0ZUVycm9yKTtcbiAgICAgIHRocm93IHVwZGF0ZUVycm9yO1xuICAgIH1cblxuICAgIGlmICghdXBkYXRlUmVzdWx0IHx8IHVwZGF0ZVJlc3VsdC5sZW5ndGggPT09IDApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTm8gcm93cyB3ZXJlIHVwZGF0ZWQgLSB0aGlzIG1pZ2h0IGJlIGEgcGVybWlzc2lvbnMgaXNzdWUnKTtcbiAgICB9XG5cbiAgICBpZiAodXBkYXRlUmVzdWx0Lmxlbmd0aCA+IDEpIHtcbiAgICAgIGNvbnNvbGUud2FybignTXVsdGlwbGUgcm93cyB1cGRhdGVkLCByZXR1cm5pbmcgZmlyc3Qgb25lJyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHVwZGF0ZVJlc3VsdFswXTtcbiAgfSxcblxuICAvLyBEZWxldGUgbWVudSBpdGVtICh1c2luZyBhZG1pbiBjbGllbnQpXG4gIGFzeW5jIGRlbGV0ZShpZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ21lbnVfaXRlbXMnKVxuICAgICAgLmRlbGV0ZSgpXG4gICAgICAuZXEoJ2lkJywgaWQpO1xuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvcjtcbiAgfVxufTtcblxuLy8gQVBJIGZ1bmN0aW9ucyBmb3IgcmVzdGF1cmFudCBpbmZvXG5leHBvcnQgY29uc3QgcmVzdGF1cmFudEFQSSA9IHtcbiAgLy8gR2V0IHJlc3RhdXJhbnQgaW5mb3JtYXRpb25cbiAgYXN5bmMgZ2V0SW5mbygpOiBQcm9taXNlPFJlc3RhdXJhbnRJbmZvIHwgbnVsbD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgncmVzdGF1cmFudF9pbmZvJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmxpbWl0KDEpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhO1xuICB9LFxuXG4gIC8vIFVwZGF0ZSByZXN0YXVyYW50IGluZm9ybWF0aW9uXG4gIGFzeW5jIHVwZGF0ZUluZm8odXBkYXRlczogUGFydGlhbDxSZXN0YXVyYW50SW5mbz4pOiBQcm9taXNlPFJlc3RhdXJhbnRJbmZvPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdyZXN0YXVyYW50X2luZm8nKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVzKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhO1xuICB9XG59O1xuXG4vLyBBUEkgZnVuY3Rpb25zIGZvciBhZG1pbiB1c2Vyc1xuZXhwb3J0IGNvbnN0IGFkbWluQVBJID0ge1xuICAvLyBHZXQgYWxsIGFkbWluIHVzZXJzXG4gIGFzeW5jIGdldEFsbCgpOiBQcm9taXNlPEFkbWluVXNlcltdPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhZG1pbl91c2VycycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgcmV0dXJuIGRhdGEgfHwgW107XG4gIH0sXG5cbiAgLy8gR2V0IGFkbWluIHVzZXIgYnkgZW1haWxcbiAgYXN5bmMgZ2V0QnlFbWFpbChlbWFpbDogc3RyaW5nKTogUHJvbWlzZTxBZG1pblVzZXIgfCBudWxsPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdhZG1pbl91c2VycycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgnZW1haWwnLCBlbWFpbClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH0sXG5cbiAgLy8gQ3JlYXRlIG5ldyBhZG1pbiB1c2VyXG4gIGFzeW5jIGNyZWF0ZSh1c2VyOiBPbWl0PEFkbWluVXNlciwgJ2lkJyB8ICdjcmVhdGVkX2F0JyB8ICd1cGRhdGVkX2F0Jz4pOiBQcm9taXNlPEFkbWluVXNlcj4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxuICAgICAgLmluc2VydCh1c2VyKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhO1xuICB9LFxuXG4gIC8vIFVwZGF0ZSBhZG1pbiB1c2VyXG4gIGFzeW5jIHVwZGF0ZShpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPEFkbWluVXNlcj4pOiBQcm9taXNlPEFkbWluVXNlcj4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVzKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICAgIHJldHVybiBkYXRhO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2UiLCJjcmVhdGVBZG1pbkNsaWVudCIsInN1cGFiYXNlU2VydmljZUtleSIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJFcnJvciIsImF1dGgiLCJhdXRvUmVmcmVzaFRva2VuIiwicGVyc2lzdFNlc3Npb24iLCJzdXBhYmFzZUFkbWluIiwiY2F0ZWdvcnlBUEkiLCJnZXRBbGwiLCJkYXRhIiwiZXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJvcmRlciIsImFzY2VuZGluZyIsImdldEJ5SWQiLCJpZCIsInNpbmdsZSIsImNyZWF0ZSIsImNhdGVnb3J5IiwiaW5zZXJ0IiwidXBkYXRlIiwidXBkYXRlcyIsImRlbGV0ZSIsIm1lbnVBUEkiLCJnZXRCeUNhdGVnb3J5IiwiY2F0ZWdvcnlJZCIsImdldEZlYXR1cmVkIiwiaXRlbSIsImNvbnNvbGUiLCJsb2ciLCJleGlzdGluZ0l0ZW0iLCJjaGVja0Vycm9yIiwibWVzc2FnZSIsInVwZGF0ZVJlc3VsdCIsInVwZGF0ZUVycm9yIiwicm93Q291bnQiLCJsZW5ndGgiLCJ3YXJuIiwicmVzdGF1cmFudEFQSSIsImdldEluZm8iLCJsaW1pdCIsInVwZGF0ZUluZm8iLCJhZG1pbkFQSSIsImdldEJ5RW1haWwiLCJlbWFpbCIsInVzZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});