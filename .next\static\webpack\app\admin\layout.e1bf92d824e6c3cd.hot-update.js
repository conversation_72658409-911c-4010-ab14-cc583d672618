"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Create Supabase client for public operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Create Supabase admin client for server-side operations (bypasses RLS)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item\n    async create (item) {\n        const { data, error } = await supabase.from('menu_items').insert(item).select(\"\\n        *,\\n        category:categories(*)\\n      \").single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item\n    async update (id, updates) {\n        console.log('Updating menu item in database:', id, updates);\n        // First check if the item exists\n        const { data: existingItem, error: checkError } = await supabase.from('menu_items').select('*').eq('id', id).single();\n        console.log('Existing item check:', {\n            existingItem,\n            checkError\n        });\n        if (checkError || !existingItem) {\n            throw new Error(\"Menu item with ID \".concat(id, \" not found: \").concat((checkError === null || checkError === void 0 ? void 0 : checkError.message) || 'No data returned'));\n        }\n        // Try the update without .single() first to see what happens\n        const { data: updateResult, error: updateError } = await supabase.from('menu_items').update(updates).eq('id', id).select(\"\\n        *,\\n        category:categories(*)\\n      \");\n        console.log('Update result:', {\n            updateResult,\n            updateError,\n            rowCount: updateResult === null || updateResult === void 0 ? void 0 : updateResult.length\n        });\n        if (updateError) {\n            console.error('Update error:', updateError);\n            throw updateError;\n        }\n        if (!updateResult || updateResult.length === 0) {\n            throw new Error('No rows were updated - this might be a permissions issue');\n        }\n        if (updateResult.length > 1) {\n            console.warn('Multiple rows updated, returning first one');\n        }\n        return updateResult[0];\n    },\n    // Delete menu item\n    async delete (id) {\n        const { error } = await supabase.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});