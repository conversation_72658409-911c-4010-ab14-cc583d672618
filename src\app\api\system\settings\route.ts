import { NextRequest, NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// GET /api/system/settings - Get system settings
export async function GET(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser, error: adminError } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    // Get system settings (in production, these would be stored in database)
    const settings = {
      security: {
        session_timeout: 30, // minutes
        max_login_attempts: 5,
        password_policy: {
          min_length: 8,
          require_uppercase: true,
          require_lowercase: true,
          require_numbers: true,
          require_symbols: false,
        },
        two_factor_enabled: false,
        ip_whitelist_enabled: false,
        allowed_ips: [],
      },
      performance: {
        cache_enabled: true,
        cache_ttl: 300, // seconds
        compression_enabled: true,
        image_optimization: true,
        lazy_loading: true,
        cdn_enabled: false,
      },
      backup: {
        auto_backup_enabled: true,
        backup_frequency: 'daily',
        backup_retention_days: 30,
        backup_location: 'supabase',
        last_backup: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
      notifications: {
        email_notifications: true,
        admin_email: '<EMAIL>',
        error_notifications: true,
        performance_alerts: true,
        backup_notifications: true,
      },
      maintenance: {
        maintenance_mode: false,
        maintenance_message: 'We are currently performing scheduled maintenance. Please check back soon.',
        scheduled_maintenance: null,
      },
    };

    return NextResponse.json({ settings });

  } catch (error) {
    console.error('Error fetching system settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch system settings' },
      { status: 500 }
    );
  }
}

// POST /api/system/settings - Update system settings
export async function POST(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser, error: adminError } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    const { settings } = await request.json();

    // In production, you would save these settings to a database
    // For now, we'll just validate and return success
    
    // Log the settings update
    console.log('System settings updated by:', user.email);
    console.log('Updated settings:', settings);

    return NextResponse.json({ 
      message: 'Settings updated successfully',
      timestamp: new Date().toISOString(),
      updated_by: user.email
    });

  } catch (error) {
    console.error('Error updating system settings:', error);
    return NextResponse.json(
      { error: 'Failed to update system settings' },
      { status: 500 }
    );
  }
}
