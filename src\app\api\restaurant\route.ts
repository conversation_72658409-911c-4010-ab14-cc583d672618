import { NextRequest, NextResponse } from 'next/server';
import { restaurantAPI } from '@/lib/supabase';

// GET /api/restaurant - Get restaurant information
export async function GET() {
  try {
    const restaurantInfo = await restaurantAPI.getInfo();
    return NextResponse.json(restaurantInfo);
  } catch (error) {
    console.error('Error fetching restaurant info:', error);
    return NextResponse.json(
      { error: 'Failed to fetch restaurant information' },
      { status: 500 }
    );
  }
}

// PUT /api/restaurant - Update restaurant information (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    const updatedInfo = await restaurantAPI.updateInfo(body);
    return NextResponse.json(updatedInfo);
  } catch (error) {
    console.error('Error updating restaurant info:', error);
    return NextResponse.json(
      { error: 'Failed to update restaurant information' },
      { status: 500 }
    );
  }
}
