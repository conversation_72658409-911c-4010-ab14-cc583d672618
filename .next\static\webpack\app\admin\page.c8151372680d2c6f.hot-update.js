"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Coffee,MenuIcon,Star,Users,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const { user, isITAdmin, isRestaurantAdmin } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            const fetchStats = {\n                \"AdminDashboard.useEffect.fetchStats\": async ()=>{\n                    try {\n                        // Fetch categories\n                        const categoriesResponse = await fetch('/api/categories');\n                        const categories = await categoriesResponse.json();\n                        // Fetch menu items\n                        const menuResponse = await fetch('/api/menu');\n                        const menuItems = await menuResponse.json();\n                        const featuredItems = menuItems.filter({\n                            \"AdminDashboard.useEffect.fetchStats\": (item)=>item.is_featured\n                        }[\"AdminDashboard.useEffect.fetchStats\"]).length;\n                        setStats({\n                            totalMenuItems: menuItems.length,\n                            totalCategories: categories.length,\n                            featuredItems,\n                            lastUpdated: new Date().toLocaleDateString()\n                        });\n                    } catch (error) {\n                        console.error('Error fetching dashboard stats:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdminDashboard.useEffect.fetchStats\"];\n            fetchStats();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const statCards = [\n        {\n            title: 'Menu Items',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalMenuItems) || 0,\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-50',\n            description: 'Total dishes available'\n        },\n        {\n            title: 'Categories',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalCategories) || 0,\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-green-600',\n            bgColor: 'bg-green-50',\n            description: 'Menu categories'\n        },\n        {\n            title: 'Featured Items',\n            value: (stats === null || stats === void 0 ? void 0 : stats.featuredItems) || 0,\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-yellow-600',\n            bgColor: 'bg-yellow-50',\n            description: 'Highlighted dishes'\n        },\n        {\n            title: 'Last Updated',\n            value: (stats === null || stats === void 0 ? void 0 : stats.lastUpdated) || 'Loading...',\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'text-purple-600',\n            bgColor: 'bg-purple-50',\n            description: 'Menu last modified',\n            isDate: true\n        }\n    ];\n    const quickActions = [\n        {\n            title: 'Manage Menu',\n            description: 'Add, edit, or remove menu items',\n            href: '/admin/menu',\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'bg-blue-500',\n            available: true\n        },\n        {\n            title: 'Restaurant Info',\n            description: 'Update restaurant details',\n            href: '/admin/restaurant',\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'bg-green-500',\n            available: true\n        },\n        {\n            title: 'Admin Users',\n            description: 'Manage admin accounts',\n            href: '/admin/users',\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: 'bg-purple-500',\n            available: isITAdmin\n        },\n        {\n            title: 'System Settings',\n            description: 'Configure system preferences',\n            href: '/admin/settings',\n            icon: _barrel_optimize_names_BarChart3_Clock_Coffee_MenuIcon_Star_Users_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: 'bg-orange-500',\n            available: isITAdmin\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h1, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-3xl font-bold text-foreground\",\n                        children: [\n                            \"Welcome back, \",\n                            (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.email)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                        initial: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.1\n                        },\n                        className: \"text-muted-foreground mt-2\",\n                        children: isITAdmin ? 'You have full system access as an IT Administrator' : 'Manage your restaurant menu and information'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: statCards.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"bg-card rounded-xl p-6 border border-border shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-foreground mt-2\",\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-6 bg-muted animate-pulse rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, this) : stat.isDate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this) : stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-1\",\n                                            children: stat.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 rounded-lg \".concat(stat.bgColor, \" flex items-center justify-center\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        size: 24,\n                                        className: stat.color\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    }, stat.title, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                        initial: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"text-xl font-semibold text-foreground mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: quickActions.filter((action)=>action.available).map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                                href: action.href,\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.4 + index * 0.1\n                                },\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                className: \"bg-card rounded-xl p-6 border border-border shadow-sm hover:shadow-md transition-all duration-200 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-lg \".concat(action.color, \" flex items-center justify-center mb-4 group-hover:scale-110 transition-transform\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                            size: 24,\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-foreground mb-2\",\n                                        children: action.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, action.title, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.6\n                },\n                className: \"bg-card rounded-xl p-6 border border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-foreground mb-4\",\n                        children: \"System Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-foreground\",\n                                        children: \"Database connection: Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-foreground\",\n                                        children: \"Authentication: Working\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-foreground\",\n                                        children: \"Menu API: Operational\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            isITAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-foreground\",\n                                        children: \"Admin privileges: Full access\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"mHjuMZtZYI2jlosNHshYhQSIbPw=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});