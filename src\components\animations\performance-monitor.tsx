'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceStats {
  fps: number;
  memoryUsage: number;
  animationCount: number;
  lastUpdate: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showInProduction?: boolean;
}

export function PerformanceMonitor({ 
  enabled = process.env.NODE_ENV === 'development',
  position = 'top-right',
  showInProduction = false
}: PerformanceMonitorProps) {
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 0,
    memoryUsage: 0,
    animationCount: 0,
    lastUpdate: Date.now()
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!enabled && !showInProduction) return;

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const updateStats = () => {
      const currentTime = performance.now();
      frameCount++;

      // Calculate FPS every second
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        // Get memory usage if available
        const memoryUsage = (performance as any).memory 
          ? Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)
          : 0;

        // Count active animations (approximate)
        const animationCount = document.querySelectorAll('[data-framer-motion]').length;

        setStats({
          fps,
          memoryUsage,
          animationCount,
          lastUpdate: Date.now()
        });

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(updateStats);
    };

    animationId = requestAnimationFrame(updateStats);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enabled, showInProduction]);

  if (!enabled && !showInProduction) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  const getFpsColor = (fps: number) => {
    if (fps >= 55) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <motion.div
      className={`fixed ${positionClasses[position]} z-50`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-mono border border-white/20 hover:bg-black/90 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        📊 Performance
      </motion.button>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full mt-2 bg-black/90 backdrop-blur-sm text-white p-4 rounded-lg text-sm font-mono border border-white/20 min-w-[200px]"
          >
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>FPS:</span>
                <span className={getFpsColor(stats.fps)}>{stats.fps}</span>
              </div>
              
              {stats.memoryUsage > 0 && (
                <div className="flex justify-between">
                  <span>Memory:</span>
                  <span className="text-blue-400">{stats.memoryUsage}MB</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>Animations:</span>
                <span className="text-purple-400">{stats.animationCount}</span>
              </div>
              
              <div className="border-t border-white/20 pt-2 mt-2">
                <div className="text-xs text-gray-400">
                  Last update: {new Date(stats.lastUpdate).toLocaleTimeString()}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

// Hook for monitoring animation performance
export function useAnimationPerformance() {
  const [performanceData, setPerformanceData] = useState({
    averageFps: 0,
    frameDrops: 0,
    isOptimal: true
  });

  useEffect(() => {
    let frameCount = 0;
    let frameDrops = 0;
    let lastTime = performance.now();
    let fpsHistory: number[] = [];

    const monitorFrame = () => {
      const currentTime = performance.now();
      const deltaTime = currentTime - lastTime;
      
      // Detect frame drops (assuming 60fps target)
      if (deltaTime > 20) { // More than ~50fps
        frameDrops++;
      }

      frameCount++;
      
      // Update stats every 60 frames
      if (frameCount >= 60) {
        const fps = 1000 / (deltaTime);
        fpsHistory.push(fps);
        
        // Keep only last 10 measurements
        if (fpsHistory.length > 10) {
          fpsHistory.shift();
        }
        
        const averageFps = fpsHistory.reduce((a, b) => a + b, 0) / fpsHistory.length;
        
        setPerformanceData({
          averageFps: Math.round(averageFps),
          frameDrops,
          isOptimal: averageFps >= 55 && frameDrops < 5
        });
        
        frameCount = 0;
        frameDrops = 0;
      }
      
      lastTime = currentTime;
      requestAnimationFrame(monitorFrame);
    };

    const animationId = requestAnimationFrame(monitorFrame);
    
    return () => cancelAnimationFrame(animationId);
  }, []);

  return performanceData;
}

// Component to automatically optimize animations based on performance
export function AdaptiveAnimations({ children }: { children: React.ReactNode }) {
  const { isOptimal } = useAnimationPerformance();
  
  return (
    <div data-animation-quality={isOptimal ? 'high' : 'reduced'}>
      {children}
    </div>
  );
}
