import { NextRequest, NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// PATCH /api/admin/users/[id] - Update admin user
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    const updates = await request.json();

    // Prevent users from modifying their own role or status
    if (id === user.id && (updates.role || updates.is_active === false)) {
      return NextResponse.json(
        { error: 'Cannot modify your own role or deactivate your own account' },
        { status: 403 }
      );
    }

    // Update admin user record using admin client
    const { data: updatedUser, error: dbError } = await supabaseAdmin
      .from('admin_users')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (dbError) {
      throw dbError;
    }

    // If updating user metadata, also update auth user
    if (updates.full_name || updates.role) {
      const { error: authUpdateError } = await supabaseAdmin.auth.admin.updateUserById(id, {
        user_metadata: {
          full_name: updates.full_name || updatedUser.full_name,
          role: updates.role || updatedUser.role,
          is_admin: true,
        }
      });

      if (authUpdateError) {
        console.error('Error updating auth user metadata:', authUpdateError);
        // Don't fail the request, just log the error
      }
    }

    console.log(`Admin user ${id} updated by ${user.email}:`, updates);

    return NextResponse.json({
      message: 'Admin user updated successfully',
      user: updatedUser
    });

  } catch (error) {
    console.error('Error updating admin user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update admin user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete admin user
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    // Prevent users from deleting themselves
    if (id === user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 403 }
      );
    }

    // Get user info before deletion using admin client
    const { data: userToDelete } = await supabaseAdmin
      .from('admin_users')
      .select('email, full_name')
      .eq('id', id)
      .single();

    // Delete from admin_users table using admin client
    const { error: dbError } = await supabaseAdmin
      .from('admin_users')
      .delete()
      .eq('id', id);

    if (dbError) {
      throw dbError;
    }

    // Delete from Supabase Auth
    const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(id);

    if (authError) {
      console.error('Error deleting auth user:', authError);
      // Don't fail the request, just log the error
    }

    console.log(`Admin user ${userToDelete?.email} deleted by ${user.email}`);

    return NextResponse.json({
      message: 'Admin user deleted successfully',
      deleted_user: userToDelete
    });

  } catch (error) {
    console.error('Error deleting admin user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete admin user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
