'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/components/auth-provider';
import { 
  Coffee, 
  MapPin, 
  Phone, 
  Clock, 
  Mail, 
  Globe, 
  Save,
  Loader2,
  Check,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface RestaurantInfo {
  id?: string;
  name: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  opening_hours: {
    [key: string]: { open: string; close: string; closed?: boolean };
  };
  social_media: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  updated_at?: string;
}

const defaultHours = {
  monday: { open: '09:00', close: '22:00' },
  tuesday: { open: '09:00', close: '22:00' },
  wednesday: { open: '09:00', close: '22:00' },
  thursday: { open: '09:00', close: '22:00' },
  friday: { open: '09:00', close: '23:00' },
  saturday: { open: '09:00', close: '23:00' },
  sunday: { open: '10:00', close: '21:00' },
};

export default function RestaurantInfoPage() {
  const { user } = useAuth();
  const [info, setInfo] = useState<RestaurantInfo>({
    name: "Chachi's Ethiopian Restaurant",
    description: "Authentic Ethiopian cuisine in the heart of Addis Ababa. Experience traditional flavors and warm hospitality.",
    address: "Bole Road, Addis Ababa, Ethiopia",
    phone: "+251 11 123 4567",
    email: "<EMAIL>",
    website: "https://chachis.et",
    opening_hours: defaultHours,
    social_media: {
      facebook: "https://facebook.com/chachisrestaurant",
      instagram: "https://instagram.com/chachisrestaurant",
      twitter: "https://twitter.com/chachisrest"
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);

  const days = [
    'monday', 'tuesday', 'wednesday', 'thursday', 
    'friday', 'saturday', 'sunday'
  ];

  const handleInputChange = (field: keyof RestaurantInfo, value: any) => {
    setInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleHoursChange = (day: string, field: 'open' | 'close' | 'closed', value: string | boolean) => {
    setInfo(prev => ({
      ...prev,
      opening_hours: {
        ...prev.opening_hours,
        [day]: {
          ...prev.opening_hours[day],
          [field]: value
        }
      }
    }));
  };

  const handleSocialChange = (platform: string, value: string) => {
    setInfo(prev => ({
      ...prev,
      social_media: {
        ...prev.social_media,
        [platform]: value
      }
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call - in real implementation, this would save to database
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setLastSaved(new Date().toLocaleString());
      toast.success('Restaurant information updated successfully!');
    } catch (error) {
      console.error('Error saving restaurant info:', error);
      toast.error('Failed to save restaurant information');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="p-6 space-y-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-3xl font-bold text-foreground"
          >
            Restaurant Information
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-muted-foreground mt-2"
          >
            Manage your restaurant's basic information and settings
          </motion.p>
        </div>
        
        <motion.button
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          onClick={handleSave}
          disabled={saving}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
        >
          {saving ? (
            <Loader2 size={16} className="animate-spin" />
          ) : (
            <Save size={16} />
          )}
          {saving ? 'Saving...' : 'Save Changes'}
        </motion.button>
      </div>

      {lastSaved && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-3 py-2 rounded-lg"
        >
          <Check size={16} />
          Last saved: {lastSaved}
        </motion.div>
      )}

      {/* Basic Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Coffee className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Basic Information</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Restaurant Name
            </label>
            <input
              type="text"
              value={info.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={info.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={info.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Website
            </label>
            <input
              type="url"
              value={info.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Address
          </label>
          <input
            type="text"
            value={info.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Description
          </label>
          <textarea
            value={info.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary resize-none"
          />
        </div>
      </motion.div>

      {/* Opening Hours */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Clock className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Opening Hours</h2>
        </div>

        <div className="space-y-4">
          {days.map((day) => (
            <div key={day} className="flex items-center gap-4">
              <div className="w-24">
                <span className="text-sm font-medium text-foreground capitalize">
                  {day}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={!info.opening_hours[day]?.closed}
                  onChange={(e) => handleHoursChange(day, 'closed', !e.target.checked)}
                  className="rounded border-border"
                />
                <span className="text-sm text-muted-foreground">Open</span>
              </div>

              {!info.opening_hours[day]?.closed && (
                <>
                  <input
                    type="time"
                    value={info.opening_hours[day]?.open || '09:00'}
                    onChange={(e) => handleHoursChange(day, 'open', e.target.value)}
                    className="px-3 py-1 border border-border rounded bg-background text-foreground text-sm"
                  />
                  <span className="text-muted-foreground">to</span>
                  <input
                    type="time"
                    value={info.opening_hours[day]?.close || '22:00'}
                    onChange={(e) => handleHoursChange(day, 'close', e.target.value)}
                    className="px-3 py-1 border border-border rounded bg-background text-foreground text-sm"
                  />
                </>
              )}
              
              {info.opening_hours[day]?.closed && (
                <span className="text-sm text-muted-foreground italic">Closed</span>
              )}
            </div>
          ))}
        </div>
      </motion.div>

      {/* Social Media */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Globe className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Social Media</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Facebook
            </label>
            <input
              type="url"
              value={info.social_media.facebook || ''}
              onChange={(e) => handleSocialChange('facebook', e.target.value)}
              placeholder="https://facebook.com/yourpage"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Instagram
            </label>
            <input
              type="url"
              value={info.social_media.instagram || ''}
              onChange={(e) => handleSocialChange('instagram', e.target.value)}
              placeholder="https://instagram.com/yourpage"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Twitter
            </label>
            <input
              type="url"
              value={info.social_media.twitter || ''}
              onChange={(e) => handleSocialChange('twitter', e.target.value)}
              placeholder="https://twitter.com/yourpage"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>
      </motion.div>
    </div>
  );
}
