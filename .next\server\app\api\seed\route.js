/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/seed/route";
exports.ids = ["app/api/seed/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Kraimatic_Desktop_Jossy_Restaurant_chachis_src_app_api_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/seed/route.ts */ \"(rsc)/./src/app/api/seed/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/seed/route\",\n        pathname: \"/api/seed\",\n        filename: \"route\",\n        bundlePath: \"app/api/seed/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\api\\\\seed\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Kraimatic_Desktop_Jossy_Restaurant_chachis_src_app_api_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzZWVkJTJGcm91dGUmcGFnZT0lMkZhcGklMkZzZWVkJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc2VlZCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNLcmFpbWF0aWMlNUNEZXNrdG9wJTVDSm9zc3klMjBSZXN0YXVyYW50JTVDY2hhY2hpcyU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDS3JhaW1hdGljJTVDRGVza3RvcCU1Q0pvc3N5JTIwUmVzdGF1cmFudCU1Q2NoYWNoaXMmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ3VDO0FBQ3BIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxKb3NzeSBSZXN0YXVyYW50XFxcXGNoYWNoaXNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc2VlZFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc2VlZC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3NlZWRcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3NlZWQvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxKb3NzeSBSZXN0YXVyYW50XFxcXGNoYWNoaXNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc2VlZFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/seed/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/seed/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_seed_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/seed-database */ \"(rsc)/./src/lib/seed-database.ts\");\n\n\n// POST /api/seed - Seed the database with sample data\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action } = body;\n        if (action === 'clear') {\n            await (0,_lib_seed_database__WEBPACK_IMPORTED_MODULE_1__.clearDatabase)();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Database cleared successfully'\n            });\n        } else {\n            const result = await (0,_lib_seed_database__WEBPACK_IMPORTED_MODULE_1__.seedDatabase)();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Database seeded successfully',\n                result\n            });\n        }\n    } catch (error) {\n        console.error('Error in seed API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to seed database',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/seed - Check database status\nasync function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Seed API is available. Use POST with {\"action\": \"seed\"} to seed database or {\"action\": \"clear\"} to clear it.'\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to check seed status'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/seed/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seed-database.ts":
/*!**********************************!*\
  !*** ./src/lib/seed-database.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   seedDatabase: () => (/* binding */ seedDatabase)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\nasync function seedDatabase() {\n    try {\n        console.log('Starting database seeding...');\n        // First, let's check if categories exist, if not create them\n        const { data: existingCategories } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*');\n        if (!existingCategories || existingCategories.length === 0) {\n            console.log('Creating categories...');\n            const { data: categories, error: categoriesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert([\n                {\n                    name: 'Appetizers',\n                    description: 'Start your meal with these delicious appetizers',\n                    display_order: 1,\n                    is_active: true\n                },\n                {\n                    name: 'Main Dishes',\n                    description: 'Traditional Ethiopian main courses',\n                    display_order: 2,\n                    is_active: true\n                },\n                {\n                    name: 'Vegetarian',\n                    description: 'Delicious vegetarian options',\n                    display_order: 3,\n                    is_active: true\n                },\n                {\n                    name: 'Beverages',\n                    description: 'Traditional and modern drinks',\n                    display_order: 4,\n                    is_active: true\n                }\n            ]).select();\n            if (categoriesError) {\n                console.error('Error creating categories:', categoriesError);\n                throw categoriesError;\n            }\n            console.log('Categories created:', categories);\n        } else {\n            console.log('Categories already exist:', existingCategories);\n        }\n        // Get categories for menu items\n        const { data: categories } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').order('display_order');\n        if (!categories || categories.length === 0) {\n            throw new Error('No categories found');\n        }\n        // Check if menu items exist\n        const { data: existingMenuItems } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select('*');\n        if (!existingMenuItems || existingMenuItems.length === 0) {\n            console.log('Creating menu items...');\n            const appetizersCategory = categories.find((c)=>c.name === 'Appetizers');\n            const mainDishesCategory = categories.find((c)=>c.name === 'Main Dishes');\n            const vegetarianCategory = categories.find((c)=>c.name === 'Vegetarian');\n            const beveragesCategory = categories.find((c)=>c.name === 'Beverages');\n            const menuItems = [\n                // Appetizers\n                {\n                    name: 'Sambusa',\n                    description: 'Crispy pastry filled with lentils and spices',\n                    price: 8.99,\n                    currency: 'USD',\n                    category_id: appetizersCategory?.id,\n                    is_available: true,\n                    is_featured: true,\n                    spice_level: 2,\n                    dietary_info: [\n                        'vegetarian'\n                    ],\n                    ingredients: [\n                        'lentils',\n                        'pastry',\n                        'onions',\n                        'spices'\n                    ],\n                    preparation_time: 15,\n                    display_order: 1\n                },\n                {\n                    name: 'Timatim Salad',\n                    description: 'Fresh tomato and onion salad with Ethiopian spices',\n                    price: 6.99,\n                    currency: 'USD',\n                    category_id: appetizersCategory?.id,\n                    is_available: true,\n                    is_featured: false,\n                    spice_level: 1,\n                    dietary_info: [\n                        'vegetarian',\n                        'vegan'\n                    ],\n                    ingredients: [\n                        'tomatoes',\n                        'onions',\n                        'jalapeños',\n                        'olive oil'\n                    ],\n                    preparation_time: 10,\n                    display_order: 2\n                },\n                // Main Dishes\n                {\n                    name: 'Doro Wot',\n                    description: 'Traditional Ethiopian chicken stew with berbere spice',\n                    price: 18.99,\n                    currency: 'USD',\n                    category_id: mainDishesCategory?.id,\n                    is_available: true,\n                    is_featured: true,\n                    spice_level: 4,\n                    dietary_info: [],\n                    ingredients: [\n                        'chicken',\n                        'berbere spice',\n                        'onions',\n                        'eggs'\n                    ],\n                    preparation_time: 45,\n                    display_order: 1\n                },\n                {\n                    name: 'Kitfo',\n                    description: 'Ethiopian steak tartare seasoned with mitmita spice',\n                    price: 22.99,\n                    currency: 'USD',\n                    category_id: mainDishesCategory?.id,\n                    is_available: true,\n                    is_featured: true,\n                    spice_level: 3,\n                    dietary_info: [],\n                    ingredients: [\n                        'beef',\n                        'mitmita spice',\n                        'clarified butter'\n                    ],\n                    preparation_time: 20,\n                    display_order: 2\n                },\n                // Vegetarian\n                {\n                    name: 'Shiro',\n                    description: 'Ground chickpea stew with Ethiopian spices',\n                    price: 14.99,\n                    currency: 'USD',\n                    category_id: vegetarianCategory?.id,\n                    is_available: true,\n                    is_featured: true,\n                    spice_level: 3,\n                    dietary_info: [\n                        'vegetarian',\n                        'vegan'\n                    ],\n                    ingredients: [\n                        'chickpeas',\n                        'berbere spice',\n                        'onions',\n                        'garlic'\n                    ],\n                    preparation_time: 30,\n                    display_order: 1\n                },\n                {\n                    name: 'Gomen',\n                    description: 'Collard greens cooked with garlic and ginger',\n                    price: 12.99,\n                    currency: 'USD',\n                    category_id: vegetarianCategory?.id,\n                    is_available: true,\n                    is_featured: false,\n                    spice_level: 2,\n                    dietary_info: [\n                        'vegetarian',\n                        'vegan'\n                    ],\n                    ingredients: [\n                        'collard greens',\n                        'garlic',\n                        'ginger',\n                        'onions'\n                    ],\n                    preparation_time: 25,\n                    display_order: 2\n                },\n                // Beverages\n                {\n                    name: 'Ethiopian Coffee',\n                    description: 'Traditional Ethiopian coffee ceremony',\n                    price: 4.99,\n                    currency: 'USD',\n                    category_id: beveragesCategory?.id,\n                    is_available: true,\n                    is_featured: true,\n                    spice_level: 0,\n                    dietary_info: [\n                        'vegetarian',\n                        'vegan'\n                    ],\n                    ingredients: [\n                        'Ethiopian coffee beans'\n                    ],\n                    preparation_time: 15,\n                    display_order: 1\n                },\n                {\n                    name: 'Tej',\n                    description: 'Traditional Ethiopian honey wine',\n                    price: 8.99,\n                    currency: 'USD',\n                    category_id: beveragesCategory?.id,\n                    is_available: true,\n                    is_featured: false,\n                    spice_level: 0,\n                    dietary_info: [\n                        'vegetarian'\n                    ],\n                    ingredients: [\n                        'honey',\n                        'water',\n                        'hops'\n                    ],\n                    preparation_time: 5,\n                    display_order: 2\n                }\n            ];\n            const { data: createdMenuItems, error: menuError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').insert(menuItems).select();\n            if (menuError) {\n                console.error('Error creating menu items:', menuError);\n                throw menuError;\n            }\n            console.log('Menu items created:', createdMenuItems);\n        } else {\n            console.log('Menu items already exist:', existingMenuItems.length, 'items');\n        }\n        console.log('Database seeding completed successfully!');\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error seeding database:', error);\n        throw error;\n    }\n}\n// Function to clear all data (for testing)\nasync function clearDatabase() {\n    try {\n        console.log('Clearing database...');\n        // Delete menu items first (due to foreign key constraints)\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').delete().neq('id', '00000000-0000-0000-0000-000000000000');\n        // Delete categories\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').delete().neq('id', '00000000-0000-0000-0000-000000000000');\n        console.log('Database cleared successfully!');\n    } catch (error) {\n        console.error('Error clearing database:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seed-database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item\n    async create (item) {\n        const { data, error } = await supabase.from('menu_items').insert(item).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item\n    async update (id, updates) {\n        console.log('Updating menu item in database:', id, updates);\n        // First check if the item exists\n        const { data: existingItem, error: checkError } = await supabase.from('menu_items').select('*').eq('id', id).single();\n        console.log('Existing item check:', {\n            existingItem,\n            checkError\n        });\n        if (checkError || !existingItem) {\n            throw new Error(`Menu item with ID ${id} not found: ${checkError?.message || 'No data returned'}`);\n        }\n        // Try the update without .single() first to see what happens\n        const { data: updateResult, error: updateError } = await supabase.from('menu_items').update(updates).eq('id', id).select(`\n        *,\n        category:categories(*)\n      `);\n        console.log('Update result:', {\n            updateResult,\n            updateError,\n            rowCount: updateResult?.length\n        });\n        if (updateError) {\n            console.error('Update error:', updateError);\n            throw updateError;\n        }\n        if (!updateResult || updateResult.length === 0) {\n            throw new Error('No rows were updated - this might be a permissions issue');\n        }\n        if (updateResult.length > 1) {\n            console.warn('Multiple rows updated, returning first one');\n        }\n        return updateResult[0];\n    },\n    // Delete menu item\n    async delete (id) {\n        const { error } = await supabase.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();