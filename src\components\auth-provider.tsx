'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, createClientAuth, roleUtils } from '@/lib/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  isITAdmin: boolean;
  isRestaurantAdmin: boolean;
  isAdmin: boolean;
  canManageMenu: boolean;
  canManageSystem: boolean;
  canManageAdmins: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const auth = createClientAuth();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const currentUser = await auth.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = auth.onAuthStateChange((user) => {
      setUser(user);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      await auth.signIn(email, password);
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      await auth.signOut();
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signOut,
    isITAdmin: roleUtils.isITAdmin(user),
    isRestaurantAdmin: roleUtils.isRestaurantAdmin(user),
    isAdmin: roleUtils.isAdmin(user),
    canManageMenu: roleUtils.canManageMenu(user),
    canManageSystem: roleUtils.canManageSystem(user),
    canManageAdmins: roleUtils.canManageAdmins(user),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireAdmin?: boolean;
    requireITAdmin?: boolean;
    requireRestaurantAdmin?: boolean;
    redirectTo?: string;
  } = {}
) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth();
    const [shouldRender, setShouldRender] = useState(false);

    useEffect(() => {
      if (loading) return;

      // Check authentication requirements
      if (!user) {
        if (typeof window !== 'undefined') {
          window.location.href = options.redirectTo || '/auth/login';
        }
        return;
      }

      // Check role requirements
      if (options.requireITAdmin && !roleUtils.isITAdmin(user)) {
        if (typeof window !== 'undefined') {
          window.location.href = '/unauthorized';
        }
        return;
      }

      if (options.requireRestaurantAdmin && !roleUtils.isRestaurantAdmin(user)) {
        if (typeof window !== 'undefined') {
          window.location.href = '/unauthorized';
        }
        return;
      }

      if (options.requireAdmin && !roleUtils.isAdmin(user)) {
        if (typeof window !== 'undefined') {
          window.location.href = '/unauthorized';
        }
        return;
      }

      setShouldRender(true);
    }, [user, loading]);

    if (loading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      );
    }

    if (!shouldRender) {
      return null;
    }

    return <Component {...props} />;
  };
}

// Hook for checking permissions
export function usePermissions() {
  const { user } = useAuth();
  
  return {
    canManageMenu: roleUtils.canManageMenu(user),
    canManageSystem: roleUtils.canManageSystem(user),
    canManageAdmins: roleUtils.canManageAdmins(user),
    isITAdmin: roleUtils.isITAdmin(user),
    isRestaurantAdmin: roleUtils.isRestaurantAdmin(user),
    isAdmin: roleUtils.isAdmin(user),
  };
}
