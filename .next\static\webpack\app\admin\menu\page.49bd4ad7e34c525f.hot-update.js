"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/menu/page",{

/***/ "(app-pages-browser)/./src/app/admin/menu/page.tsx":
/*!*************************************!*\
  !*** ./src/app/admin/menu/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MenuManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Edit,Eye,EyeOff,Grid,List,Plus,Search,Star,Trash2,Utensils!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _components_menu_item_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/menu-item-form */ \"(app-pages-browser)/./src/components/menu-item-form.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction MenuManagementPage() {\n    _s();\n    const { user } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Core state\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [menuItems, setMenuItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // UI state\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingItem, setEditingItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedItems, setSelectedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBulkActions, setShowBulkActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: 'all',\n        availability: 'all',\n        featured: 'all',\n        priceRange: {\n            min: 0,\n            max: 1000\n        },\n        spiceLevel: 'all'\n    });\n    const [sortOptions, setSortOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        field: 'display_order',\n        direction: 'asc'\n    });\n    // Stats state\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalItems: 0,\n        availableItems: 0,\n        featuredItems: 0,\n        totalCategories: 0,\n        averagePrice: 0,\n        recentlyUpdated: 0,\n        outOfStock: 0,\n        revenue: 0\n    });\n    // Fetch data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MenuManagementPage.useEffect\": ()=>{\n            const fetchData = {\n                \"MenuManagementPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch categories and menu items\n                        const [categoriesRes, menuRes] = await Promise.all([\n                            fetch('/api/categories'),\n                            fetch('/api/menu')\n                        ]);\n                        const categoriesData = await categoriesRes.json();\n                        const menuData = await menuRes.json();\n                        setCategories(categoriesData);\n                        setMenuItems(menuData);\n                        setFilteredItems(menuData);\n                        // Calculate stats\n                        setStats({\n                            totalItems: menuData.length,\n                            availableItems: menuData.filter({\n                                \"MenuManagementPage.useEffect.fetchData\": (item)=>item.is_available\n                            }[\"MenuManagementPage.useEffect.fetchData\"]).length,\n                            featuredItems: menuData.filter({\n                                \"MenuManagementPage.useEffect.fetchData\": (item)=>item.is_featured\n                            }[\"MenuManagementPage.useEffect.fetchData\"]).length,\n                            totalCategories: categoriesData.length\n                        });\n                    } catch (error) {\n                        console.error('Error fetching menu data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"MenuManagementPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"MenuManagementPage.useEffect\"], []);\n    // Filter items based on search and category\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MenuManagementPage.useEffect\": ()=>{\n            let filtered = menuItems;\n            // Filter by search term\n            if (searchTerm) {\n                filtered = filtered.filter({\n                    \"MenuManagementPage.useEffect\": (item)=>{\n                        var _item_description;\n                        return item.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_item_description = item.description) === null || _item_description === void 0 ? void 0 : _item_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                    }\n                }[\"MenuManagementPage.useEffect\"]);\n            }\n            // Filter by category\n            if (selectedCategory !== 'all') {\n                filtered = filtered.filter({\n                    \"MenuManagementPage.useEffect\": (item)=>item.category_id === selectedCategory\n                }[\"MenuManagementPage.useEffect\"]);\n            }\n            setFilteredItems(filtered);\n        }\n    }[\"MenuManagementPage.useEffect\"], [\n        menuItems,\n        searchTerm,\n        selectedCategory\n    ]);\n    const handleToggleAvailability = async (item)=>{\n        try {\n            const response = await fetch(\"/api/menu/\".concat(item.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    is_available: !item.is_available\n                })\n            });\n            if (response.ok) {\n                const updatedItem = await response.json();\n                setMenuItems((prev)=>prev.map((i)=>i.id === item.id ? updatedItem : i));\n            }\n        } catch (error) {\n            console.error('Error updating item availability:', error);\n        }\n    };\n    const handleToggleFeatured = async (item)=>{\n        try {\n            const response = await fetch(\"/api/menu/\".concat(item.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    is_featured: !item.is_featured\n                })\n            });\n            if (response.ok) {\n                const updatedItem = await response.json();\n                setMenuItems((prev)=>prev.map((i)=>i.id === item.id ? updatedItem : i));\n            }\n        } catch (error) {\n            console.error('Error updating featured status:', error);\n        }\n    };\n    const handleDeleteItem = async (item)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(item.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/menu/\".concat(item.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setMenuItems((prev)=>prev.filter((i)=>i.id !== item.id));\n            }\n        } catch (error) {\n            console.error('Error deleting item:', error);\n        }\n    };\n    const handleSaveItem = async (itemData)=>{\n        try {\n            const url = editingItem ? \"/api/menu/\".concat(editingItem.id) : '/api/menu';\n            const method = editingItem ? 'PATCH' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(itemData)\n            });\n            if (response.ok) {\n                const savedItem = await response.json();\n                if (editingItem) {\n                    setMenuItems((prev)=>prev.map((i)=>i.id === editingItem.id ? savedItem : i));\n                } else {\n                    setMenuItems((prev)=>[\n                            ...prev,\n                            savedItem\n                        ]);\n                }\n                setShowCreateModal(false);\n                setEditingItem(null);\n            }\n        } catch (error) {\n            console.error('Error saving item:', error);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setShowCreateModal(false);\n        setEditingItem(null);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Menu Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Manage your restaurant's menu items and categories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        onClick: ()=>setShowCreateModal(true),\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Menu Item\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    {\n                        title: 'Total Items',\n                        value: stats.totalItems,\n                        icon: _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        color: 'text-blue-600'\n                    },\n                    {\n                        title: 'Available',\n                        value: stats.availableItems,\n                        icon: _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        color: 'text-green-600'\n                    },\n                    {\n                        title: 'Featured',\n                        value: stats.featuredItems,\n                        icon: _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        color: 'text-yellow-600'\n                    },\n                    {\n                        title: 'Categories',\n                        value: stats.totalCategories,\n                        icon: _barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        color: 'text-purple-600'\n                    }\n                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"bg-card p-6 rounded-lg border shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-foreground\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                    className: \"h-8 w-8 \".concat(stat.color)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    }, stat.title, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search menu items...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedCategory,\n                        onChange: (e)=>setSelectedCategory(e.target.value),\n                        className: \"px-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"all\",\n                                children: \"All Categories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this),\n                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: category.id,\n                                    children: category.name\n                                }, category.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 border border-border rounded-lg p-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setViewMode('grid'),\n                                className: \"p-2 rounded \".concat(viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setViewMode('list'),\n                                className: \"p-2 rounded \".concat(viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4',\n                children: filteredItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MenuItemCard, {\n                        item: item,\n                        index: index,\n                        viewMode: viewMode,\n                        onToggleAvailability: ()=>handleToggleAvailability(item),\n                        onToggleFeatured: ()=>handleToggleFeatured(item),\n                        onEdit: ()=>setEditingItem(item),\n                        onDelete: ()=>handleDeleteItem(item)\n                    }, item.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            filteredItems.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-muted-foreground mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-foreground mb-2\",\n                        children: \"No menu items found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mb-4\",\n                        children: searchTerm || selectedCategory !== 'all' ? 'Try adjusting your search or filter criteria.' : 'Get started by adding your first menu item.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateModal(true),\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            \"Add Menu Item\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 360,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_item_form__WEBPACK_IMPORTED_MODULE_3__.MenuItemForm, {\n                item: editingItem,\n                categories: categories,\n                onSave: handleSaveItem,\n                onCancel: handleCloseModal,\n                isOpen: showCreateModal || editingItem !== null\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_s(MenuManagementPage, \"JHDAdj5FOaCGWWX7u4c+d/RxZHw=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = MenuManagementPage;\nfunction MenuItemCard(param) {\n    let { item, index, viewMode, onToggleAvailability, onToggleFeatured, onEdit, onDelete } = param;\n    var _item_category;\n    _s1();\n    const [showActions, setShowActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (viewMode === 'list') {\n        var _item_category1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            initial: {\n                opacity: 0,\n                x: -20\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            },\n            transition: {\n                delay: index * 0.05\n            },\n            className: \"bg-card border rounded-lg p-4 hover:shadow-md transition-shadow\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 flex-1\",\n                        children: [\n                            item.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: item.image_url,\n                                alt: item.name,\n                                className: \"w-16 h-16 object-cover rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-foreground\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            item.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 38\n                                            }, this),\n                                            !item.is_available && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 40\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground line-clamp-1\",\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-foreground\",\n                                                children: [\n                                                    item.price,\n                                                    \" \",\n                                                    item.currency\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: (_item_category1 = item.category) === null || _item_category1 === void 0 ? void 0 : _item_category1.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleAvailability,\n                                className: \"p-2 rounded-lg transition-colors \".concat(item.is_available ? 'text-green-600 hover:bg-green-50' : 'text-muted-foreground hover:bg-muted'),\n                                title: item.is_available ? 'Hide item' : 'Show item',\n                                children: item.is_available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 36\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 56\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFeatured,\n                                className: \"p-2 rounded-lg transition-colors \".concat(item.is_featured ? 'text-yellow-500 hover:bg-yellow-50' : 'text-muted-foreground hover:bg-muted'),\n                                title: item.is_featured ? 'Remove from featured' : 'Add to featured',\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16,\n                                    className: item.is_featured ? 'fill-current' : ''\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\",\n                                title: \"Edit item\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onDelete,\n                                className: \"p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors\",\n                                title: \"Delete item\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 421,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            delay: index * 0.1\n        },\n        className: \"bg-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow group\",\n        onMouseEnter: ()=>setShowActions(true),\n        onMouseLeave: ()=>setShowActions(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-48 bg-muted\",\n                children: [\n                    item.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: item.image_url,\n                        alt: item.name,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50 flex items-center justify-center gap-2 transition-opacity \".concat(showActions ? 'opacity-100' : 'opacity-0'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleAvailability,\n                                className: \"p-2 rounded-lg bg-white/90 transition-colors \".concat(item.is_available ? 'text-green-600' : 'text-muted-foreground'),\n                                title: item.is_available ? 'Hide item' : 'Show item',\n                                children: item.is_available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 54\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFeatured,\n                                className: \"p-2 rounded-lg bg-white/90 transition-colors \".concat(item.is_featured ? 'text-yellow-500' : 'text-muted-foreground'),\n                                title: item.is_featured ? 'Remove from featured' : 'Add to featured',\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16,\n                                    className: item.is_featured ? 'fill-current' : ''\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"p-2 rounded-lg bg-white/90 text-blue-600 transition-colors\",\n                                title: \"Edit item\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onDelete,\n                                className: \"p-2 rounded-lg bg-white/90 text-red-600 transition-colors\",\n                                title: \"Delete item\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 flex gap-1\",\n                        children: [\n                            item.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-yellow-500 text-white text-xs rounded-full flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Edit_Eye_EyeOff_Grid_List_Plus_Search_Star_Trash2_Utensils_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 12,\n                                        className: \"fill-current\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Featured\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 13\n                            }, this),\n                            !item.is_available && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-red-500 text-white text-xs rounded-full\",\n                                children: \"Hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 500,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-foreground line-clamp-1\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold text-primary\",\n                                children: [\n                                    item.price,\n                                    \" \",\n                                    item.currency\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground line-clamp-2 mb-3\",\n                        children: item.description || 'No description available'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: (_item_category = item.category) === null || _item_category === void 0 ? void 0 : _item_category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, this),\n                            item.spice_level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: Array.from({\n                                    length: item.spice_level\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n                lineNumber: 571,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\admin\\\\menu\\\\page.tsx\",\n        lineNumber: 491,\n        columnNumber: 5\n    }, this);\n}\n_s1(MenuItemCard, \"9EzFePNaqmNh8mYL6UPNi0UvsSQ=\");\n_c1 = MenuItemCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"MenuManagementPage\");\n$RefreshReg$(_c1, \"MenuItemCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/menu/page.tsx\n"));

/***/ })

});