'use client';

import { motion } from 'framer-motion';
import { Heart, MapPin, Phone, Mail, Clock } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Contact",
      items: [
        { icon: MapPin, text: "Bole Road, Near Edna Mall, Addis Ababa" },
        { icon: Phone, text: "+251 11 123 4567" },
        { icon: Mail, text: "<EMAIL>" },
      ]
    },
    {
      title: "Hours",
      items: [
        { icon: Clock, text: "Monday - Sunday" },
        { text: "11:00 AM - 11:00 PM" },
        { text: "Coffee Ceremony: 3:00 PM - 6:00 PM" },
      ]
    },
    {
      title: "Experience",
      items: [
        { text: "Traditional Ethiopian Cuisine" },
        { text: "Coffee Ceremony" },
        { text: "Cultural Events" },
        { text: "Private Dining" },
      ]
    }
  ];

  return (
    <footer className="bg-secondary text-secondary-foreground">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <h3 className="text-3xl font-serif font-bold text-primary mb-4">
                Chachi's
              </h3>
              <p className="text-secondary-foreground/80 leading-relaxed mb-6">
                Where authentic Ethiopian flavors meet elegant dining. 
                Experience the warmth of Ethiopian hospitality in every meal.
              </p>
              
              {/* Ethiopian Pattern Decoration */}
              <div className="flex gap-2">
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.3, delay: i * 0.1 }}
                    viewport={{ once: true }}
                    className="w-3 h-3 bg-primary/60 rounded-full"
                  />
                ))}
              </div>
            </motion.div>

            {/* Footer Sections */}
            {footerSections.map((section, sectionIndex) => (
              <motion.div
                key={section.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: (sectionIndex + 1) * 0.1 }}
                viewport={{ once: true }}
              >
                <h4 className="text-lg font-semibold text-primary mb-4">
                  {section.title}
                </h4>
                <ul className="space-y-3">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2">
                      {item.icon && (
                        <item.icon size={16} className="text-primary mt-0.5 flex-shrink-0" />
                      )}
                      <span className="text-secondary-foreground/80 text-sm">
                        {item.text}
                      </span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Newsletter Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
          className="border-t border-secondary-foreground/20 py-8"
        >
          <div className="max-w-md mx-auto text-center">
            <h4 className="text-lg font-semibold text-primary mb-2">
              Stay Connected
            </h4>
            <p className="text-secondary-foreground/80 text-sm mb-4">
              Subscribe to receive updates about special events and new menu items
            </p>
            <div className="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 rounded-full bg-background/10 border border-secondary-foreground/20 text-secondary-foreground placeholder-secondary-foreground/60 focus:outline-none focus:border-primary"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-primary text-primary-foreground px-6 py-2 rounded-full font-medium hover:bg-primary/90 transition-colors duration-200"
              >
                Subscribe
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          viewport={{ once: true }}
          className="border-t border-secondary-foreground/20 py-6"
        >
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-secondary-foreground/60 text-sm">
              © {currentYear} Chachi's Restaurant. All rights reserved.
            </p>
            
            <div className="flex items-center gap-2 text-secondary-foreground/60 text-sm">
              <span>Made with</span>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                <Heart size={16} className="text-accent fill-current" />
              </motion.div>
              <span>in Addis Ababa</span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
