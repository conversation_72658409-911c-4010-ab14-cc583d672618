'use client';

import { motion, useInView, useAnimation, AnimatePresence } from 'framer-motion';
import { useRef, useEffect, ReactNode } from 'react';
import {
  staggerContainer,
  staggerItem,
  cardHover,
  buttonVariants,
  slideInUp,
  scaleIn,
  floating,
  pulseVariants,
  spinnerVariants,
  typewriterContainer,
  typewriterItem,
} from './animation-variants';

// Animated container that triggers animations when in view
interface AnimatedSectionProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  stagger?: boolean;
}

export function AnimatedSection({ 
  children, 
  className = '', 
  delay = 0, 
  stagger = false 
}: AnimatedSectionProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('animate');
    }
  }, [isInView, controls]);

  return (
    <motion.div
      ref={ref}
      initial="initial"
      animate={controls}
      variants={stagger ? staggerContainer : slideInUp}
      transition={{ delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Animated card with hover effects
interface AnimatedCardProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  delay?: number;
}

export function AnimatedCard({ 
  children, 
  className = '', 
  onClick, 
  delay = 0 
}: AnimatedCardProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-50px' });

  return (
    <motion.div
      ref={ref}
      initial="initial"
      animate={isInView ? 'animate' : 'initial'}
      whileHover="hover"
      whileTap="tap"
      variants={{
        ...scaleIn,
        animate: {
          ...scaleIn.animate,
          transition: {
            ...scaleIn.animate.transition,
            delay,
          },
        },
        ...cardHover,
      }}
      onClick={onClick}
      className={`cursor-pointer ${className}`}
    >
      {children}
    </motion.div>
  );
}

// Animated button with enhanced interactions
interface AnimatedButtonProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
}

export function AnimatedButton({
  children,
  className = '',
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
}: AnimatedButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground',
    ghost: 'text-primary hover:bg-primary/10',
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  return (
    <motion.button
      variants={buttonVariants}
      initial="initial"
      whileHover={!disabled && !loading ? "hover" : undefined}
      whileTap={!disabled && !loading ? "tap" : undefined}
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
    >
      <AnimatePresence mode="wait">
        {loading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-2"
          >
            <motion.div
              variants={spinnerVariants}
              animate="animate"
              className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
            />
            Loading...
          </motion.div>
        ) : (
          <motion.span
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {children}
          </motion.span>
        )}
      </AnimatePresence>
    </motion.button>
  );
}

// Staggered list animation
interface AnimatedListProps {
  children: ReactNode[];
  className?: string;
  staggerDelay?: number;
}

export function AnimatedList({ 
  children, 
  className = '', 
  staggerDelay = 0.1 
}: AnimatedListProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-50px' });

  return (
    <motion.div
      ref={ref}
      initial="initial"
      animate={isInView ? 'animate' : 'initial'}
      variants={{
        initial: {},
        animate: {
          transition: {
            staggerChildren: staggerDelay,
            delayChildren: 0.2,
          },
        },
      }}
      className={className}
    >
      {children.map((child, index) => (
        <motion.div
          key={index}
          variants={staggerItem}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Floating element animation
interface FloatingElementProps {
  children: ReactNode;
  className?: string;
  intensity?: number;
  duration?: number;
}

export function FloatingElement({ 
  children, 
  className = '', 
  intensity = 10, 
  duration = 3 
}: FloatingElementProps) {
  return (
    <motion.div
      animate={{
        y: [0, -intensity, 0],
        transition: {
          duration,
          repeat: Infinity,
          ease: 'easeInOut',
        },
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Typewriter text effect
interface TypewriterTextProps {
  text: string;
  className?: string;
  delay?: number;
}

export function TypewriterText({ 
  text, 
  className = '', 
  delay = 0 
}: TypewriterTextProps) {
  const letters = text.split('');

  return (
    <motion.span
      variants={typewriterContainer}
      initial="initial"
      animate="animate"
      transition={{ delay }}
      className={className}
    >
      {letters.map((letter, index) => (
        <motion.span
          key={index}
          variants={typewriterItem}
        >
          {letter === ' ' ? '\u00A0' : letter}
        </motion.span>
      ))}
    </motion.span>
  );
}

// Pulsing element
interface PulsingElementProps {
  children: ReactNode;
  className?: string;
}

export function PulsingElement({ children, className = '' }: PulsingElementProps) {
  return (
    <motion.div
      variants={pulseVariants}
      animate="animate"
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Reveal on scroll component
interface RevealOnScrollProps {
  children: ReactNode;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
}

export function RevealOnScroll({ 
  children, 
  className = '', 
  direction = 'up', 
  delay = 0 
}: RevealOnScrollProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  const directionVariants = {
    up: { y: 50, opacity: 0 },
    down: { y: -50, opacity: 0 },
    left: { x: 50, opacity: 0 },
    right: { x: -50, opacity: 0 },
  };

  return (
    <motion.div
      ref={ref}
      initial={directionVariants[direction]}
      animate={isInView ? { x: 0, y: 0, opacity: 1 } : directionVariants[direction]}
      transition={{ 
        duration: 0.6, 
        ease: [0.4, 0, 0.2, 1], 
        delay 
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
