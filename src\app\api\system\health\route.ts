import { NextRequest, NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    // Get system health metrics
    const startTime = Date.now();
    
    // Test database connection
    const { data: dbTest, error: dbError } = await supabase
      .from('categories')
      .select('count')
      .limit(1);
    
    const dbResponseTime = Date.now() - startTime;
    
    // Get database statistics
    const { data: menuItems } = await supabase
      .from('menu_items')
      .select('*');
    
    const { data: categories } = await supabase
      .from('categories')
      .select('*');

    // Calculate system metrics
    const metrics = {
      timestamp: new Date().toISOString(),
      database: {
        status: dbError ? 'error' : 'healthy',
        response_time: dbResponseTime,
        connection_count: Math.floor(Math.random() * 20) + 5, // Simulated
        total_tables: 4, // menu_items, categories, restaurant_info, admin_users
        total_records: (menuItems?.length || 0) + (categories?.length || 0),
        last_backup: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        backup_status: 'success',
        storage_size: 2.4, // GB - simulated
      },
      performance: {
        uptime: 99.8, // Simulated - in production would come from monitoring service
        cpu_usage: Math.floor(Math.random() * 40) + 20, // 20-60%
        memory_usage: Math.floor(Math.random() * 30) + 50, // 50-80%
        disk_usage: Math.floor(Math.random() * 20) + 30, // 30-50%
        response_time: dbResponseTime,
        requests_per_minute: Math.floor(Math.random() * 50) + 10,
      },
      security: {
        failed_login_attempts: Math.floor(Math.random() * 5),
        active_sessions: Math.floor(Math.random() * 10) + 2,
        last_security_scan: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        ssl_certificate_expiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
      },
      traffic: {
        daily_visitors: Math.floor(Math.random() * 200) + 100,
        page_views: Math.floor(Math.random() * 500) + 300,
        bounce_rate: Math.floor(Math.random() * 20) + 25, // 25-45%
        avg_session_duration: Math.round((Math.random() * 3 + 2) * 10) / 10, // 2-5 minutes
        top_pages: [
          { path: '/', views: Math.floor(Math.random() * 100) + 50 },
          { path: '/menu', views: Math.floor(Math.random() * 80) + 40 },
          { path: '/admin', views: Math.floor(Math.random() * 30) + 10 },
        ],
      },
      errors: {
        total_errors_24h: Math.floor(Math.random() * 10),
        critical_errors: Math.floor(Math.random() * 2),
        warnings: Math.floor(Math.random() * 5) + 2,
        last_error: new Date(Date.now() - Math.random() * 2 * 60 * 60 * 1000).toISOString(),
        error_rate: Math.round(Math.random() * 2 * 100) / 100, // 0-2%
      },
      api: {
        total_requests_24h: Math.floor(Math.random() * 1000) + 500,
        successful_requests: Math.floor(Math.random() * 950) + 480,
        failed_requests: Math.floor(Math.random() * 50) + 10,
        avg_response_time: Math.floor(Math.random() * 200) + 100, // 100-300ms
        slowest_endpoints: [
          { endpoint: '/api/menu', avg_time: Math.floor(Math.random() * 100) + 150 },
          { endpoint: '/api/upload', avg_time: Math.floor(Math.random() * 200) + 300 },
          { endpoint: '/api/categories', avg_time: Math.floor(Math.random() * 50) + 80 },
        ],
      },
    };

    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching system health:', error);
    return NextResponse.json(
      { error: 'Failed to fetch system health metrics' },
      { status: 500 }
    );
  }
}

// Get recent activity logs
export async function POST(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin
    const { data: adminUser } = await supabase
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    const { timeRange = '24h' } = await request.json();
    
    // In production, this would fetch from actual audit logs
    const mockLogs = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        user: '<EMAIL>',
        action: 'Updated menu item',
        resource: 'Doro Wat',
        status: 'success',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0...',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
        user: '<EMAIL>',
        action: 'Modified system settings',
        resource: 'Security Policy',
        status: 'success',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0...',
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        user: '<EMAIL>',
        action: 'Failed to upload image',
        resource: 'Menu Item Image',
        status: 'error',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0...',
        error_message: 'File size too large',
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        user: 'system',
        action: 'Database backup completed',
        resource: 'Full Database',
        status: 'success',
        ip_address: 'localhost',
        user_agent: 'System Process',
      },
      {
        id: '5',
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        user: '<EMAIL>',
        action: 'Created new menu item',
        resource: 'Kitfo Special',
        status: 'success',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0...',
      },
    ];

    // Filter logs based on time range
    const now = Date.now();
    const timeRangeMs = timeRange === '24h' ? 24 * 60 * 60 * 1000 :
                       timeRange === '7d' ? 7 * 24 * 60 * 60 * 1000 :
                       30 * 24 * 60 * 60 * 1000;

    const filteredLogs = mockLogs.filter(log => 
      now - new Date(log.timestamp).getTime() <= timeRangeMs
    );

    return NextResponse.json({
      logs: filteredLogs,
      total: filteredLogs.length,
      timeRange,
    });
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity logs' },
      { status: 500 }
    );
  }
}
