/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/menu/[id]/route";
exports.ids = ["app/api/menu/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Kraimatic_Desktop_Jossy_Restaurant_chachis_src_app_api_menu_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/menu/[id]/route.ts */ \"(rsc)/./src/app/api/menu/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/menu/[id]/route\",\n        pathname: \"/api/menu/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/menu/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\api\\\\menu\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Kraimatic_Desktop_Jossy_Restaurant_chachis_src_app_api_menu_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/menu/[id]/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/menu/[id]/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n// GET /api/menu/[id] - Get specific menu item\nasync function GET(request, { params }) {\n    try {\n        const menuItem = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.menuAPI.getById(params.id);\n        if (!menuItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Menu item not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(menuItem);\n    } catch (error) {\n        console.error('Error fetching menu item:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch menu item'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/menu/[id] - Update menu item (admin only)\nasync function PATCH(request, { params }) {\n    try {\n        const body = await request.json();\n        // Remove undefined values and prepare update object\n        const updates = {};\n        if (body.name !== undefined) updates.name = body.name;\n        if (body.description !== undefined) updates.description = body.description;\n        if (body.price !== undefined) updates.price = parseFloat(body.price);\n        if (body.currency !== undefined) updates.currency = body.currency;\n        if (body.image_url !== undefined) updates.image_url = body.image_url;\n        if (body.is_available !== undefined) updates.is_available = body.is_available;\n        if (body.is_featured !== undefined) updates.is_featured = body.is_featured;\n        if (body.spice_level !== undefined) updates.spice_level = body.spice_level;\n        if (body.dietary_info !== undefined) updates.dietary_info = body.dietary_info;\n        if (body.ingredients !== undefined) updates.ingredients = body.ingredients;\n        if (body.allergens !== undefined) updates.allergens = body.allergens;\n        if (body.preparation_time !== undefined) updates.preparation_time = body.preparation_time;\n        if (body.display_order !== undefined) updates.display_order = body.display_order;\n        if (body.category_id !== undefined) updates.category_id = body.category_id;\n        const updatedItem = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.menuAPI.update(params.id, updates);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedItem);\n    } catch (error) {\n        console.error('Error updating menu item:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update menu item'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/menu/[id] - Delete menu item (admin only)\nasync function DELETE(request, { params }) {\n    try {\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.menuAPI.delete(params.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Error deleting menu item:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete menu item'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/menu/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item\n    async create (item) {\n        const { data, error } = await supabase.from('menu_items').insert(item).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item\n    async update (id, updates) {\n        const { data, error } = await supabase.from('menu_items').update(updates).eq('id', id).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete menu item\n    async delete (id) {\n        const { error } = await supabase.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();