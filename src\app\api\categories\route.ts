import { NextRequest, NextResponse } from 'next/server';
import { categoryAPI } from '@/lib/supabase';

// GET /api/categories - Get all active categories
export async function GET() {
  try {
    const categories = await categoryAPI.getAll();
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create new category (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      );
    }

    const category = await categoryAPI.create({
      name: body.name,
      description: body.description || null,
      icon: body.icon || null,
      display_order: body.display_order || 0,
      is_active: body.is_active !== undefined ? body.is_active : true,
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
