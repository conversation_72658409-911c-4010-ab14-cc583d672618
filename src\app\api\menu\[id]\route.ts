import { NextRequest, NextResponse } from 'next/server';
import { menuAPI } from '@/lib/supabase';

// GET /api/menu/[id] - Get specific menu item
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const menuItem = await menuAPI.getById(id);

    if (!menuItem) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(menuItem);
  } catch (error) {
    console.error('Error fetching menu item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch menu item' },
      { status: 500 }
    );
  }
}

// PATCH /api/menu/[id] - Update menu item (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Remove undefined values and prepare update object
    const updates: any = {};

    if (body.name !== undefined) updates.name = body.name;
    if (body.description !== undefined) updates.description = body.description;
    if (body.price !== undefined) updates.price = parseFloat(body.price);
    if (body.currency !== undefined) updates.currency = body.currency;
    if (body.image_url !== undefined) updates.image_url = body.image_url;
    if (body.is_available !== undefined) updates.is_available = body.is_available;
    if (body.is_featured !== undefined) updates.is_featured = body.is_featured;
    if (body.spice_level !== undefined) updates.spice_level = body.spice_level;
    if (body.dietary_info !== undefined) updates.dietary_info = body.dietary_info;
    if (body.ingredients !== undefined) updates.ingredients = body.ingredients;
    if (body.allergens !== undefined) updates.allergens = body.allergens;
    if (body.preparation_time !== undefined) updates.preparation_time = body.preparation_time;
    if (body.display_order !== undefined) updates.display_order = body.display_order;
    if (body.category_id !== undefined) updates.category_id = body.category_id;

    const updatedItem = await menuAPI.update(id, updates);
    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('Error updating menu item:', error);
    return NextResponse.json(
      { error: 'Failed to update menu item' },
      { status: 500 }
    );
  }
}

// DELETE /api/menu/[id] - Delete menu item (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    await menuAPI.delete(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting menu item:', error);
    return NextResponse.json(
      { error: 'Failed to delete menu item' },
      { status: 500 }
    );
  }
}
