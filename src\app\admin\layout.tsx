'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useAuth, withAuth } from '@/components/auth-provider';
import { 
  ChefHat, 
  Menu as MenuIcon, 
  Settings, 
  Users, 
  BarChart3, 
  LogOut,
  Home,
  Coffee,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface AdminLayoutProps {
  children: ReactNode;
}

function AdminLayout({ children }: AdminLayoutProps) {
  const { user, signOut, isITAdmin, isRestaurantAdmin } = useAuth();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: BarChart3,
      description: 'Overview and analytics',
      allowedRoles: ['it_admin', 'restaurant_admin'],
    },
    {
      name: 'Menu Management',
      href: '/admin/menu',
      icon: MenuIcon,
      description: 'Manage menu items and categories',
      allowedRoles: ['it_admin', 'restaurant_admin'],
    },
    {
      name: 'Restaurant Info',
      href: '/admin/restaurant',
      icon: Coffee,
      description: 'Update restaurant information',
      allowedRoles: ['it_admin', 'restaurant_admin'],
    },
    {
      name: 'Admin Users',
      href: '/admin/users',
      icon: Users,
      description: 'Manage admin accounts',
      allowedRoles: ['it_admin'],
    },
    {
      name: 'System Settings',
      href: '/admin/settings',
      icon: Settings,
      description: 'System configuration',
      allowedRoles: ['it_admin'],
    },
  ];

  const filteredNavigation = navigationItems.filter(item => 
    item.allowedRoles.includes(user?.role || 'user')
  );

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{ x: sidebarOpen ? 0 : '-100%' }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className="fixed inset-y-0 left-0 z-50 w-72 bg-card border-r border-border lg:translate-x-0 lg:static lg:inset-0"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center gap-3 p-6 border-b border-border">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <ChefHat size={24} className="text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-foreground">Chachi's Admin</h1>
              <p className="text-sm text-muted-foreground">
                {isITAdmin ? 'IT Administrator' : 'Restaurant Admin'}
              </p>
            </div>
          </div>

          {/* User Info */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                <Shield size={16} className="text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">
                  {user?.full_name || user?.email}
                </p>
                <p className="text-xs text-muted-foreground">
                  {user?.role === 'it_admin' ? 'IT Admin' : 'Restaurant Admin'}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {filteredNavigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                  }`}
                >
                  <item.icon size={20} />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{item.name}</p>
                    <p className="text-xs opacity-75">{item.description}</p>
                  </div>
                </Link>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-border space-y-2">
            <Link
              href="/"
              className="flex items-center gap-3 px-3 py-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
            >
              <Home size={20} />
              <span className="text-sm">Back to Website</span>
            </Link>
            <button
              onClick={handleSignOut}
              className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
            >
              <LogOut size={20} />
              <span className="text-sm">Sign Out</span>
            </button>
          </div>
        </div>
      </motion.aside>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Mobile header */}
        <div className="lg:hidden flex items-center justify-between p-4 border-b border-border bg-card">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 rounded-lg hover:bg-muted transition-colors"
          >
            <MenuIcon size={24} />
          </button>
          <h1 className="text-lg font-semibold">Admin Panel</h1>
          <div className="w-10" /> {/* Spacer */}
        </div>

        {/* Page content */}
        <main className="min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
}

// Wrap with authentication
export default withAuth(AdminLayout, { 
  requireAdmin: true,
  redirectTo: '/auth/login'
});
