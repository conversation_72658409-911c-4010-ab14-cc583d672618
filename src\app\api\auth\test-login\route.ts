import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/auth/test-login - Test login credentials
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password required' },
        { status: 400 }
      );
    }

    // Try to sign in with the provided credentials
    const { data, error } = await supabaseAdmin.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        code: error.status
      });
    }

    // Check admin user record
    const { data: adminUser, error: adminError } = await supabaseAdmin
      .from('admin_users')
      .select('*')
      .eq('id', data.user?.id)
      .single();

    return NextResponse.json({
      success: true,
      user: {
        id: data.user?.id,
        email: data.user?.email,
        created_at: data.user?.created_at,
      },
      adminUser: adminUser,
      adminError: adminError?.message,
      session: {
        access_token: data.session?.access_token?.substring(0, 20) + '...',
        refresh_token: data.session?.refresh_token?.substring(0, 20) + '...',
        expires_at: data.session?.expires_at,
      }
    });

  } catch (error) {
    console.error('Error in test login:', error);
    return NextResponse.json(
      { 
        error: 'Test login failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
