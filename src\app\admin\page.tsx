'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/components/auth-provider';
import { 
  BarChart3, 
  Users, 
  MenuIcon, 
  TrendingUp,
  Clock,
  Star,
  Coffee,
  Utensils
} from 'lucide-react';

interface DashboardStats {
  totalMenuItems: number;
  totalCategories: number;
  featuredItems: number;
  lastUpdated: string;
}

export default function AdminDashboard() {
  const { user, isITAdmin, isRestaurantAdmin } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');
        const categories = await categoriesResponse.json();

        // Fetch menu items
        const menuResponse = await fetch('/api/menu');
        const menuItems = await menuResponse.json();

        const featuredItems = menuItems.filter((item: any) => item.is_featured).length;

        setStats({
          totalMenuItems: menuItems.length,
          totalCategories: categories.length,
          featuredItems,
          lastUpdated: new Date().toLocaleDateString(),
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'Menu Items',
      value: stats?.totalMenuItems || 0,
      icon: Utensils,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Total dishes available',
    },
    {
      title: 'Categories',
      value: stats?.totalCategories || 0,
      icon: MenuIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Menu categories',
    },
    {
      title: 'Featured Items',
      value: stats?.featuredItems || 0,
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: 'Highlighted dishes',
    },
    {
      title: 'Last Updated',
      value: stats?.lastUpdated || 'Loading...',
      icon: Clock,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'Menu last modified',
      isDate: true,
    },
  ];

  const quickActions = [
    {
      title: 'Manage Menu',
      description: 'Add, edit, or remove menu items',
      href: '/admin/menu',
      icon: MenuIcon,
      color: 'bg-blue-500',
      available: true,
    },
    {
      title: 'Restaurant Info',
      description: 'Update restaurant details',
      href: '/admin/restaurant',
      icon: Coffee,
      color: 'bg-green-500',
      available: true,
    },
    {
      title: 'Admin Users',
      description: 'Manage admin accounts',
      href: '/admin/users',
      icon: Users,
      color: 'bg-purple-500',
      available: isITAdmin,
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      href: '/admin/settings',
      icon: BarChart3,
      color: 'bg-orange-500',
      available: isITAdmin,
    },
  ];

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div>
        <motion.h1
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-foreground"
        >
          Welcome back, {user?.full_name || user?.email}
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-muted-foreground mt-2"
        >
          {isITAdmin 
            ? 'You have full system access as an IT Administrator'
            : 'Manage your restaurant menu and information'
          }
        </motion.p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-card rounded-xl p-6 border border-border shadow-sm"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </p>
                <div className="text-2xl font-bold text-foreground mt-2">
                  {loading ? (
                    <div className="w-8 h-6 bg-muted animate-pulse rounded" />
                  ) : stat.isDate ? (
                    <span className="text-lg">{stat.value}</span>
                  ) : (
                    stat.value
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </div>
              <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                <stat.icon size={24} className={stat.color} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <motion.h2
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="text-xl font-semibold text-foreground mb-4"
        >
          Quick Actions
        </motion.h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions
            .filter(action => action.available)
            .map((action, index) => (
              <motion.a
                key={action.title}
                href={action.href}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-card rounded-xl p-6 border border-border shadow-sm hover:shadow-md transition-all duration-200 group"
              >
                <div className={`w-12 h-12 rounded-lg ${action.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                  <action.icon size={24} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  {action.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {action.description}
                </p>
              </motion.a>
            ))}
        </div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-card rounded-xl p-6 border border-border"
      >
        <h2 className="text-xl font-semibold text-foreground mb-4">
          System Status
        </h2>
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-foreground">Database connection: Active</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-foreground">Authentication: Working</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-foreground">Menu API: Operational</span>
          </div>
          {isITAdmin && (
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-foreground">Admin privileges: Full access</span>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
