'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { 
  useParallax, 
  useScrollOpacity, 
  useScrollScale, 
  useTextReveal,
  useStaggeredAnimation 
} from '@/hooks/use-scroll-animations';

// Parallax wrapper component
interface ParallaxWrapperProps {
  children: ReactNode;
  speed?: number;
  className?: string;
}

export function ParallaxWrapper({ children, speed = 0.5, className = '' }: ParallaxWrapperProps) {
  const { ref, y } = useParallax(speed);

  return (
    <motion.div
      ref={ref}
      style={{ y }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Fade in on scroll component
interface FadeInScrollProps {
  children: ReactNode;
  className?: string;
}

export function FadeInScroll({ children, className = '' }: FadeInScrollProps) {
  const { ref, opacity } = useScrollOpacity();

  return (
    <motion.div
      ref={ref}
      style={{ opacity }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Scale on scroll component
interface ScaleOnScrollProps {
  children: ReactNode;
  className?: string;
}

export function ScaleOnScroll({ children, className = '' }: ScaleOnScrollProps) {
  const { ref, scale } = useScrollScale();

  return (
    <motion.div
      ref={ref}
      style={{ scale }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Text reveal animation
interface TextRevealProps {
  text: string;
  className?: string;
  splitBy?: 'word' | 'character';
}

export function TextReveal({ text, className = '', splitBy = 'word' }: TextRevealProps) {
  const { ref, isVisible } = useTextReveal();
  const items = splitBy === 'word' ? text.split(' ') : text.split('');

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
      variants={{
        visible: {
          transition: {
            staggerChildren: splitBy === 'word' ? 0.1 : 0.05,
          },
        },
      }}
    >
      {items.map((item, index) => (
        <motion.span
          key={index}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0 },
          }}
          className="inline-block"
        >
          {item}
          {splitBy === 'word' && index < items.length - 1 && '\u00A0'}
        </motion.span>
      ))}
    </motion.div>
  );
}

// Staggered reveal component
interface StaggeredRevealProps {
  children: ReactNode[];
  className?: string;
  delay?: number;
}

export function StaggeredReveal({ children, className = '', delay = 0.1 }: StaggeredRevealProps) {
  const { ref, isVisible, variants } = useStaggeredAnimation(children.length, delay);

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
    >
      {children.map((child, index) => (
        <motion.div
          key={index}
          custom={index}
          variants={variants}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Scroll-triggered counter animation
interface CounterAnimationProps {
  from: number;
  to: number;
  duration?: number;
  className?: string;
  suffix?: string;
  prefix?: string;
}

export function CounterAnimation({ 
  from, 
  to, 
  duration = 2, 
  className = '', 
  suffix = '', 
  prefix = '' 
}: CounterAnimationProps) {
  const { ref, isVisible } = useTextReveal();

  return (
    <motion.div
      ref={ref}
      className={className}
      initial={{ opacity: 0 }}
      animate={isVisible ? { opacity: 1 } : { opacity: 0 }}
    >
      <motion.span
        initial={{ textContent: from }}
        animate={isVisible ? { textContent: to } : { textContent: from }}
        transition={{ duration, ease: 'easeOut' }}
        onUpdate={(latest) => {
          if (ref.current) {
            ref.current.textContent = `${prefix}${Math.round(latest.textContent)}${suffix}`;
          }
        }}
      />
    </motion.div>
  );
}

// Morphing background component
interface MorphingBackgroundProps {
  children: ReactNode;
  className?: string;
  colors?: string[];
}

export function MorphingBackground({ 
  children, 
  className = '', 
  colors = ['#d4af37', '#8b4513', '#dc143c'] 
}: MorphingBackgroundProps) {
  const { ref, opacity } = useScrollOpacity();

  return (
    <motion.div
      ref={ref}
      className={`relative ${className}`}
      style={{ opacity }}
    >
      <motion.div
        className="absolute inset-0 -z-10"
        animate={{
          background: [
            `linear-gradient(45deg, ${colors[0]}, ${colors[1]})`,
            `linear-gradient(135deg, ${colors[1]}, ${colors[2]})`,
            `linear-gradient(225deg, ${colors[2]}, ${colors[0]})`,
            `linear-gradient(315deg, ${colors[0]}, ${colors[1]})`,
          ],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
      {children}
    </motion.div>
  );
}

// Scroll progress indicator
interface ScrollProgressProps {
  className?: string;
}

export function ScrollProgress({ className = '' }: ScrollProgressProps) {
  const { ref, opacity } = useScrollOpacity();

  return (
    <motion.div
      ref={ref}
      className={`fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left ${className}`}
      style={{ 
        opacity,
        scaleX: opacity,
      }}
    />
  );
}

// Floating elements on scroll
interface FloatingOnScrollProps {
  children: ReactNode;
  intensity?: number;
  className?: string;
}

export function FloatingOnScroll({ 
  children, 
  intensity = 20, 
  className = '' 
}: FloatingOnScrollProps) {
  const { ref, y } = useParallax(0.3);

  return (
    <motion.div
      ref={ref}
      style={{ y }}
      animate={{
        y: [0, -intensity, 0],
        rotate: [0, 2, 0, -2, 0],
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
