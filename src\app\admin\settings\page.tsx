'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/components/auth-provider';
import { 
  Settings, 
  Database, 
  Shield, 
  Palette, 
  Globe, 
  Bell,
  Save,
  Loader2,
  Check,
  AlertTriangle,
  RefreshCw,
  Download,
  Upload,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';

interface SystemSettings {
  database: {
    connection_status: 'connected' | 'disconnected' | 'error';
    last_backup: string;
    auto_backup: boolean;
    backup_frequency: 'daily' | 'weekly' | 'monthly';
  };
  security: {
    session_timeout: number;
    max_login_attempts: number;
    require_2fa: boolean;
    password_policy: {
      min_length: number;
      require_uppercase: boolean;
      require_numbers: boolean;
      require_symbols: boolean;
    };
  };
  appearance: {
    theme: 'light' | 'dark' | 'system';
    primary_color: string;
    logo_url: string;
  };
  notifications: {
    email_notifications: boolean;
    order_alerts: boolean;
    system_alerts: boolean;
    maintenance_mode: boolean;
  };
}

export default function SystemSettingsPage() {
  const { user, isITAdmin } = useAuth();
  const [settings, setSettings] = useState<SystemSettings>({
    database: {
      connection_status: 'connected',
      last_backup: '2024-01-20T10:30:00Z',
      auto_backup: true,
      backup_frequency: 'daily',
    },
    security: {
      session_timeout: 30,
      max_login_attempts: 5,
      require_2fa: false,
      password_policy: {
        min_length: 8,
        require_uppercase: true,
        require_numbers: true,
        require_symbols: false,
      },
    },
    appearance: {
      theme: 'system',
      primary_color: '#d4af37',
      logo_url: '',
    },
    notifications: {
      email_notifications: true,
      order_alerts: true,
      system_alerts: true,
      maintenance_mode: false,
    },
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);

  const handleSettingChange = (section: keyof SystemSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedSettingChange = (section: keyof SystemSettings, subsection: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...(prev[section] as any)[subsection],
          [field]: value
        }
      }
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/system/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ settings }),
      });

      if (!response.ok) {
        throw new Error(`Settings API error: ${response.status}`);
      }

      const data = await response.json();
      setLastSaved(new Date().toLocaleString());
      toast.success('System settings updated successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save system settings');
    } finally {
      setSaving(false);
    }
  };

  const handleBackup = async () => {
    try {
      toast.info('Starting database backup...');

      const response = await fetch('/api/system/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'create_backup' }),
      });

      if (!response.ok) {
        throw new Error(`Backup API error: ${response.status}`);
      }

      const data = await response.json();

      setSettings(prev => ({
        ...prev,
        database: {
          ...prev.database,
          last_backup: data.timestamp
        }
      }));

      toast.success(`Database backup completed successfully! Backup ID: ${data.backup_id}`);
    } catch (error) {
      console.error('Error creating backup:', error);
      toast.error('Failed to create database backup');
    }
  };

  const handleSystemRestart = async () => {
    if (!confirm('Are you sure you want to restart the system? This will temporarily interrupt service.')) {
      return;
    }

    try {
      toast.info('System restart initiated...');

      const response = await fetch('/api/system/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'restart_system' }),
      });

      if (!response.ok) {
        throw new Error(`Restart API error: ${response.status}`);
      }

      const data = await response.json();
      toast.success(`System restart initiated successfully! Estimated downtime: ${data.estimated_downtime}`);
    } catch (error) {
      console.error('Error restarting system:', error);
      toast.error('Failed to restart system');
    }
  };

  const handleClearCache = async () => {
    try {
      toast.info('Clearing system cache...');

      const response = await fetch('/api/system/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'clear_cache' }),
      });

      if (!response.ok) {
        throw new Error(`Cache API error: ${response.status}`);
      }

      const data = await response.json();
      toast.success(`System cache cleared successfully! Cleared: ${data.cache_types_cleared.join(', ')}`);
    } catch (error) {
      console.error('Error clearing cache:', error);
      toast.error('Failed to clear system cache');
    }
  };

  if (!isITAdmin) {
    return (
      <div className="p-6 text-center">
        <div className="max-w-md mx-auto">
          <Shield size={48} className="mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            You need IT Administrator privileges to access system settings.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-3xl font-bold text-foreground"
          >
            System Settings
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-muted-foreground mt-2"
          >
            Configure system-wide settings and preferences
          </motion.p>
        </div>
        
        <motion.button
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          onClick={handleSave}
          disabled={saving}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
        >
          {saving ? (
            <Loader2 size={16} className="animate-spin" />
          ) : (
            <Save size={16} />
          )}
          {saving ? 'Saving...' : 'Save Changes'}
        </motion.button>
      </div>

      {lastSaved && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-3 py-2 rounded-lg"
        >
          <Check size={16} />
          Last saved: {lastSaved}
        </motion.div>
      )}

      {/* Database Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Database className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Database</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Connection Status
            </label>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                settings.database.connection_status === 'connected' ? 'bg-green-500' :
                settings.database.connection_status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
              }`}></div>
              <span className="text-sm text-foreground capitalize">
                {settings.database.connection_status}
              </span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Last Backup
            </label>
            <div className="flex items-center gap-2">
              <span className="text-sm text-foreground">
                {new Date(settings.database.last_backup).toLocaleString()}
              </span>
              <button
                onClick={handleBackup}
                className="p-1 text-primary hover:bg-primary/10 rounded transition-colors"
                title="Create backup now"
              >
                <RefreshCw size={14} />
              </button>
            </div>
          </div>

          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={settings.database.auto_backup}
                onChange={(e) => handleSettingChange('database', 'auto_backup', e.target.checked)}
                className="rounded border-border"
              />
              <span className="text-sm font-medium text-foreground">Auto Backup</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Backup Frequency
            </label>
            <select
              value={settings.database.backup_frequency}
              onChange={(e) => handleSettingChange('database', 'backup_frequency', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Security Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Shield className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Security</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Session Timeout (minutes)
            </label>
            <input
              type="number"
              value={settings.security.session_timeout}
              onChange={(e) => handleSettingChange('security', 'session_timeout', parseInt(e.target.value))}
              min="5"
              max="120"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Max Login Attempts
            </label>
            <input
              type="number"
              value={settings.security.max_login_attempts}
              onChange={(e) => handleSettingChange('security', 'max_login_attempts', parseInt(e.target.value))}
              min="3"
              max="10"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={settings.security.require_2fa}
                onChange={(e) => handleSettingChange('security', 'require_2fa', e.target.checked)}
                className="rounded border-border"
              />
              <span className="text-sm font-medium text-foreground">Require 2FA</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Password Min Length
            </label>
            <input
              type="number"
              value={settings.security.password_policy.min_length}
              onChange={(e) => handleNestedSettingChange('security', 'password_policy', 'min_length', parseInt(e.target.value))}
              min="6"
              max="20"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-sm font-medium text-foreground">Password Requirements</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={settings.security.password_policy.require_uppercase}
                onChange={(e) => handleNestedSettingChange('security', 'password_policy', 'require_uppercase', e.target.checked)}
                className="rounded border-border"
              />
              <span className="text-sm text-foreground">Uppercase letters</span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={settings.security.password_policy.require_numbers}
                onChange={(e) => handleNestedSettingChange('security', 'password_policy', 'require_numbers', e.target.checked)}
                className="rounded border-border"
              />
              <span className="text-sm text-foreground">Numbers</span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={settings.security.password_policy.require_symbols}
                onChange={(e) => handleNestedSettingChange('security', 'password_policy', 'require_symbols', e.target.checked)}
                className="rounded border-border"
              />
              <span className="text-sm text-foreground">Special symbols</span>
            </label>
          </div>
        </div>
      </motion.div>

      {/* Appearance Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Palette className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Appearance</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Theme
            </label>
            <select
              value={settings.appearance.theme}
              onChange={(e) => handleSettingChange('appearance', 'theme', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="system">System</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Primary Color
            </label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={settings.appearance.primary_color}
                onChange={(e) => handleSettingChange('appearance', 'primary_color', e.target.value)}
                className="w-12 h-10 border border-border rounded cursor-pointer"
              />
              <input
                type="text"
                value={settings.appearance.primary_color}
                onChange={(e) => handleSettingChange('appearance', 'primary_color', e.target.value)}
                className="flex-1 px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Notifications */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Bell className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">Notifications</h2>
        </div>

        <div className="space-y-4">
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">Email Notifications</span>
            <input
              type="checkbox"
              checked={settings.notifications.email_notifications}
              onChange={(e) => handleSettingChange('notifications', 'email_notifications', e.target.checked)}
              className="rounded border-border"
            />
          </label>
          
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">Order Alerts</span>
            <input
              type="checkbox"
              checked={settings.notifications.order_alerts}
              onChange={(e) => handleSettingChange('notifications', 'order_alerts', e.target.checked)}
              className="rounded border-border"
            />
          </label>
          
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">System Alerts</span>
            <input
              type="checkbox"
              checked={settings.notifications.system_alerts}
              onChange={(e) => handleSettingChange('notifications', 'system_alerts', e.target.checked)}
              className="rounded border-border"
            />
          </label>
          
          <label className="flex items-center justify-between">
            <div>
              <span className="text-sm font-medium text-foreground">Maintenance Mode</span>
              <p className="text-xs text-muted-foreground">Temporarily disable public access</p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.maintenance_mode}
              onChange={(e) => handleSettingChange('notifications', 'maintenance_mode', e.target.checked)}
              className="rounded border-border"
            />
          </label>
        </div>

        {settings.notifications.maintenance_mode && (
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle size={16} className="text-yellow-600" />
            <span className="text-sm text-yellow-800">
              Maintenance mode is enabled. Public users cannot access the website.
            </span>
          </div>
        )}
      </motion.div>

      {/* System Management */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-card rounded-xl p-6 border border-border space-y-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <Settings className="text-primary" size={24} />
          <h2 className="text-xl font-semibold text-foreground">System Management</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            type="button"
            onClick={handleSystemRestart}
            className="flex flex-col items-center gap-3 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors group"
          >
            <div className="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center group-hover:bg-red-100 transition-colors">
              <RefreshCw size={24} className="text-red-600" />
            </div>
            <div className="text-center">
              <h3 className="font-medium text-foreground">Restart System</h3>
              <p className="text-sm text-muted-foreground">Graceful system restart</p>
            </div>
          </button>

          <button
            type="button"
            onClick={handleClearCache}
            className="flex flex-col items-center gap-3 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors group"
          >
            <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
              <Trash2 size={24} className="text-blue-600" />
            </div>
            <div className="text-center">
              <h3 className="font-medium text-foreground">Clear Cache</h3>
              <p className="text-sm text-muted-foreground">Clear system cache</p>
            </div>
          </button>

          <button
            type="button"
            onClick={handleBackup}
            className="flex flex-col items-center gap-3 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors group"
          >
            <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center group-hover:bg-green-100 transition-colors">
              <Download size={24} className="text-green-600" />
            </div>
            <div className="text-center">
              <h3 className="font-medium text-foreground">Manual Backup</h3>
              <p className="text-sm text-muted-foreground">Create database backup</p>
            </div>
          </button>
        </div>
      </motion.div>
    </div>
  );
}
