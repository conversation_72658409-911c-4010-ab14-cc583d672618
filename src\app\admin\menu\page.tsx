'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoadingSpinner, MenuItemSkeleton, AnimatedButton } from '@/components/animations';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Star,
  ChefHat,
  Utensils,
  Grid,
  List,
  MoreVertical,
  SortAsc,
  SortDesc,
  RefreshCw,
  Download,
  Upload,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Package
} from 'lucide-react';
import { Category, MenuItem } from '@/lib/supabase';
import { useAuth } from '@/components/auth-provider';
import { MenuItemForm } from '@/components/menu-item-form';
import { toast } from 'sonner';

interface MenuStats {
  totalItems: number;
  availableItems: number;
  featuredItems: number;
  totalCategories: number;
  averagePrice: number;
  recentlyUpdated: number;
  outOfStock: number;
  revenue: number;
}

interface FilterOptions {
  category: string;
  availability: 'all' | 'available' | 'unavailable';
  featured: 'all' | 'featured' | 'regular';
  priceRange: { min: number; max: number };
  spiceLevel: 'all' | '0' | '1' | '2' | '3' | '4' | '5';
}

interface SortOptions {
  field: 'name' | 'price' | 'created_at' | 'updated_at' | 'display_order';
  direction: 'asc' | 'desc';
}

type ViewMode = 'grid' | 'list' | 'table';

export default function MenuManagementPage() {
  const { user } = useAuth();

  // Core state
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Filter and sort state
  const [filters, setFilters] = useState<FilterOptions>({
    category: 'all',
    availability: 'all',
    featured: 'all',
    priceRange: { min: 0, max: 1000 },
    spiceLevel: 'all'
  });

  const [sortOptions, setSortOptions] = useState<SortOptions>({
    field: 'display_order',
    direction: 'asc'
  });

  // Stats state
  const [stats, setStats] = useState<MenuStats>({
    totalItems: 0,
    availableItems: 0,
    featuredItems: 0,
    totalCategories: 0,
    averagePrice: 0,
    recentlyUpdated: 0,
    outOfStock: 0,
    revenue: 0
  });

  // Data fetching with error handling and caching
  const fetchData = useCallback(async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // Fetch categories and menu items in parallel
      const [categoriesResponse, menuResponse] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/menu')
      ]);

      if (!categoriesResponse.ok || !menuResponse.ok) {
        throw new Error('Failed to fetch data');
      }

      const [categoriesData, menuData] = await Promise.all([
        categoriesResponse.json(),
        menuResponse.json()
      ]);

      setCategories(categoriesData);
      setMenuItems(menuData);

      // Calculate comprehensive stats
      const totalRevenue = menuData.reduce((sum: number, item: MenuItem) =>
        sum + (item.is_available ? parseFloat(item.price.toString()) : 0), 0
      );

      const recentlyUpdated = menuData.filter((item: MenuItem) => {
        const updatedAt = new Date(item.updated_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return updatedAt > weekAgo;
      }).length;

      const newStats: MenuStats = {
        totalItems: menuData.length,
        availableItems: menuData.filter((item: MenuItem) => item.is_available).length,
        featuredItems: menuData.filter((item: MenuItem) => item.is_featured).length,
        totalCategories: categoriesData.length,
        averagePrice: menuData.length > 0 ? totalRevenue / menuData.length : 0,
        recentlyUpdated,
        outOfStock: menuData.filter((item: MenuItem) => !item.is_available).length,
        revenue: totalRevenue
      };
      setStats(newStats);

      if (showRefreshIndicator) {
        toast.success('Menu data refreshed successfully');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load menu data. Please try again.');
      toast.error('Failed to load menu data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Advanced filtering and sorting with memoization
  const filteredAndSortedItems = useMemo(() => {
    let filtered = [...menuItems];

    // Search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.description?.toLowerCase().includes(searchLower) ||
        item.ingredients?.some(ingredient =>
          ingredient.toLowerCase().includes(searchLower)
        ) ||
        item.category?.name.toLowerCase().includes(searchLower)
      );
    }

    // Category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(item => item.category_id === filters.category);
    }

    // Availability filter
    if (filters.availability !== 'all') {
      filtered = filtered.filter(item =>
        filters.availability === 'available' ? item.is_available : !item.is_available
      );
    }

    // Featured filter
    if (filters.featured !== 'all') {
      filtered = filtered.filter(item =>
        filters.featured === 'featured' ? item.is_featured : !item.is_featured
      );
    }

    // Price range filter
    filtered = filtered.filter(item => {
      const price = parseFloat(item.price.toString());
      return price >= filters.priceRange.min && price <= filters.priceRange.max;
    });

    // Spice level filter
    if (filters.spiceLevel !== 'all') {
      const targetSpiceLevel = parseInt(filters.spiceLevel);
      filtered = filtered.filter(item => item.spice_level === targetSpiceLevel);
    }

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortOptions.field) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = parseFloat(a.price.toString());
          bValue = parseFloat(b.price.toString());
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'updated_at':
          aValue = new Date(a.updated_at);
          bValue = new Date(b.updated_at);
          break;
        case 'display_order':
        default:
          aValue = a.display_order;
          bValue = b.display_order;
          break;
      }

      if (aValue < bValue) return sortOptions.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOptions.direction === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [menuItems, searchTerm, filters, sortOptions]);

  // Modern action handlers with optimistic updates and error handling
  const handleToggleAvailability = useCallback(async (item: MenuItem) => {
    const originalItems = [...menuItems];
    const newAvailability = !item.is_available;

    // Optimistic update
    setMenuItems(prev => prev.map(i =>
      i.id === item.id ? { ...i, is_available: newAvailability } : i
    ));

    try {
      const response = await fetch(`/api/menu/${item.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_available: newAvailability })
      });

      if (!response.ok) throw new Error('Failed to update availability');

      toast.success(`${item.name} is now ${newAvailability ? 'available' : 'unavailable'}`);

      // Refresh stats
      fetchData(true);
    } catch (error) {
      // Revert optimistic update
      setMenuItems(originalItems);
      console.error('Error toggling availability:', error);
      toast.error('Failed to update availability');
    }
  }, [menuItems, fetchData]);

  const handleToggleFeatured = useCallback(async (item: MenuItem) => {
    const originalItems = [...menuItems];
    const newFeatured = !item.is_featured;

    // Optimistic update
    setMenuItems(prev => prev.map(i =>
      i.id === item.id ? { ...i, is_featured: newFeatured } : i
    ));

    try {
      const response = await fetch(`/api/menu/${item.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_featured: newFeatured })
      });

      if (!response.ok) throw new Error('Failed to update featured status');

      toast.success(`${item.name} ${newFeatured ? 'added to' : 'removed from'} featured items`);

      // Refresh stats
      fetchData(true);
    } catch (error) {
      // Revert optimistic update
      setMenuItems(originalItems);
      console.error('Error toggling featured:', error);
      toast.error('Failed to update featured status');
    }
  }, [menuItems, fetchData]);

  const handleDeleteItem = useCallback(async (item: MenuItem) => {
    if (!confirm(`Are you sure you want to delete "${item.name}"? This action cannot be undone.`)) {
      return;
    }

    const originalItems = [...menuItems];

    // Optimistic update
    setMenuItems(prev => prev.filter(i => i.id !== item.id));

    try {
      const response = await fetch(`/api/menu/${item.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete item');

      toast.success(`${item.name} has been deleted`);

      // Refresh stats
      fetchData(true);
    } catch (error) {
      // Revert optimistic update
      setMenuItems(originalItems);
      console.error('Error deleting item:', error);
      toast.error('Failed to delete item');
    }
  }, [menuItems, fetchData]);

  const handleSaveItem = async (itemData: Partial<MenuItem>) => {
    try {
      const url = editingItem ? `/api/menu/${editingItem.id}` : '/api/menu';
      const method = editingItem ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(itemData)
      });

      if (response.ok) {
        const savedItem = await response.json();

        if (editingItem) {
          setMenuItems(prev => prev.map(i => i.id === editingItem.id ? savedItem : i));
        } else {
          setMenuItems(prev => [...prev, savedItem]);
        }

        setShowCreateModal(false);
        setEditingItem(null);
      }
    } catch (error) {
      console.error('Error saving item:', error);
    }
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingItem(null);
  };

  // Loading state
  if (loading) {
    return (
      <div className="p-6 space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-32 bg-muted rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-destructive mb-2">Error Loading Menu</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <button
            type="button"
            onClick={() => fetchData()}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            <RefreshCw size={16} />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6 space-y-6">
      {/* Modern Header with Actions */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="space-y-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-foreground">Menu Management</h1>
            {refreshing && (
              <RefreshCw className="h-5 w-5 text-muted-foreground animate-spin" />
            )}
          </div>
          <p className="text-muted-foreground">
            Manage your restaurant's menu items and categories with advanced tools
          </p>
        </div>

        <div className="flex items-center gap-3">
          <button
            type="button"
            onClick={() => fetchData(true)}
            disabled={refreshing}
            className="inline-flex items-center gap-2 px-3 py-2 border border-border rounded-lg hover:bg-muted/50 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
            Refresh
          </button>

          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center gap-2 px-3 py-2 border rounded-lg transition-colors ${
              showFilters
                ? 'bg-primary text-primary-foreground border-primary'
                : 'border-border hover:bg-muted/50'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>

          <motion.button
            type="button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors shadow-sm"
          >
            <Plus size={18} />
            Add Menu Item
          </motion.button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4">
        {[
          {
            title: 'Total Items',
            value: stats.totalItems,
            icon: Package,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
            change: '+12%',
            changeType: 'positive'
          },
          {
            title: 'Available',
            value: stats.availableItems,
            icon: CheckCircle,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
            change: `${stats.totalItems > 0 ? Math.round((stats.availableItems / stats.totalItems) * 100) : 0}%`,
            changeType: 'neutral'
          },
          {
            title: 'Featured',
            value: stats.featuredItems,
            icon: Star,
            color: 'text-yellow-600',
            bgColor: 'bg-yellow-50',
            change: '+3',
            changeType: 'positive'
          },
          {
            title: 'Avg Price',
            value: `$${stats.averagePrice.toFixed(2)}`,
            icon: DollarSign,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
            change: '+5.2%',
            changeType: 'positive'
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-card p-6 rounded-xl border shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className={`text-sm font-medium ${
                stat.changeType === 'positive' ? 'text-green-600' :
                stat.changeType === 'negative' ? 'text-red-600' : 'text-muted-foreground'
              }`}>
                {stat.change}
              </div>
            </div>
            <div>
              <p className="text-sm text-muted-foreground mb-1">{stat.title}</p>
              <p className="text-2xl font-bold text-foreground">{stat.value}</p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions Bar */}
      <div className="flex flex-wrap items-center gap-3 p-4 bg-muted/30 rounded-lg border">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock size={16} />
          <span>Recently Updated: {stats.recentlyUpdated}</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <AlertCircle size={16} />
          <span>Out of Stock: {stats.outOfStock}</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <TrendingUp size={16} />
          <span>Total Revenue: ${stats.revenue.toFixed(2)}</span>
        </div>
        <div className="ml-auto flex items-center gap-2">
          <button
            type="button"
            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm border border-border rounded-md hover:bg-muted/50 transition-colors"
          >
            <Download size={14} />
            Export
          </button>
          <button
            type="button"
            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm border border-border rounded-md hover:bg-muted/50 transition-colors"
          >
            <Upload size={14} />
            Import
          </button>
        </div>
      </div>

      {/* Advanced Search and Filters */}
      <div className="space-y-4">
        {/* Search Bar with Sort */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
            <input
              type="text"
              placeholder="Search menu items, ingredients, categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <div className="flex items-center gap-3">
            {/* Sort Options */}
            <select
              value={`${sortOptions.field}-${sortOptions.direction}`}
              onChange={(e) => {
                const [field, direction] = e.target.value.split('-') as [SortOptions['field'], SortOptions['direction']];
                setSortOptions({ field, direction });
              }}
              className="px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="display_order-asc">Order (A-Z)</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="price-asc">Price (Low-High)</option>
              <option value="price-desc">Price (High-Low)</option>
              <option value="created_at-desc">Newest First</option>
              <option value="updated_at-desc">Recently Updated</option>
            </select>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-1 border border-border rounded-lg p-1">
              <button
                type="button"
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                }`}
                title="Grid View"
              >
                <Grid size={18} />
              </button>
              <button
                type="button"
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'list'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                }`}
                title="List View"
              >
                <List size={18} />
              </button>
              <button
                type="button"
                onClick={() => setViewMode('table')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'table'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                }`}
                title="Table View"
              >
                <Settings size={18} />
              </button>
            </div>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-muted/30 rounded-lg border p-4 space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Category</label>
                  <select
                    value={filters.category}
                    onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="all">All Categories</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Availability Filter */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Availability</label>
                  <select
                    value={filters.availability}
                    onChange={(e) => setFilters(prev => ({ ...prev, availability: e.target.value as FilterOptions['availability'] }))}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="all">All Items</option>
                    <option value="available">Available Only</option>
                    <option value="unavailable">Unavailable Only</option>
                  </select>
                </div>

                {/* Featured Filter */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Featured</label>
                  <select
                    value={filters.featured}
                    onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.value as FilterOptions['featured'] }))}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="all">All Items</option>
                    <option value="featured">Featured Only</option>
                    <option value="regular">Regular Only</option>
                  </select>
                </div>

                {/* Spice Level Filter */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Spice Level</label>
                  <select
                    value={filters.spiceLevel}
                    onChange={(e) => setFilters(prev => ({ ...prev, spiceLevel: e.target.value as FilterOptions['spiceLevel'] }))}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="all">All Levels</option>
                    <option value="0">Mild (0)</option>
                    <option value="1">Light (1)</option>
                    <option value="2">Medium (2)</option>
                    <option value="3">Hot (3)</option>
                    <option value="4">Very Hot (4)</option>
                    <option value="5">Extreme (5)</option>
                  </select>
                </div>
              </div>

              {/* Price Range Filter */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Price Range: ${filters.priceRange.min} - ${filters.priceRange.max}
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={filters.priceRange.min}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      priceRange: { ...prev.priceRange, min: parseInt(e.target.value) }
                    }))}
                    className="flex-1"
                  />
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={filters.priceRange.max}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      priceRange: { ...prev.priceRange, max: parseInt(e.target.value) }
                    }))}
                    className="flex-1"
                  />
                </div>
              </div>

              {/* Filter Actions */}
              <div className="flex items-center justify-between pt-2 border-t border-border">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredAndSortedItems.length} of {menuItems.length} items
                </div>
                <button
                  type="button"
                  onClick={() => {
                    setFilters({
                      category: 'all',
                      availability: 'all',
                      featured: 'all',
                      priceRange: { min: 0, max: 1000 },
                      spiceLevel: 'all'
                    });
                    setSearchTerm('');
                  }}
                  className="px-3 py-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  Clear All Filters
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Menu Items Display */}
      <div className="space-y-4">
        {/* Results Header */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {filteredAndSortedItems.length} of {menuItems.length} items
          </div>
          {selectedItems.size > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {selectedItems.size} selected
              </span>
              <button
                type="button"
                onClick={() => setShowBulkActions(!showBulkActions)}
                className="px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                Bulk Actions
              </button>
            </div>
          )}
        </div>

        {/* Menu Items Grid/List */}
        {filteredAndSortedItems.length > 0 ? (
          <div className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : viewMode === 'list'
              ? 'space-y-4'
              : 'overflow-x-auto'
          }>
            {filteredAndSortedItems.map((item, index) => (
              <MenuItemCard
                key={item.id}
                item={item}
                index={index}
                viewMode={viewMode}
                onToggleAvailability={() => handleToggleAvailability(item)}
                onToggleFeatured={() => handleToggleFeatured(item)}
                onEdit={() => setEditingItem(item)}
                onDelete={() => handleDeleteItem(item)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <ChefHat className="h-20 w-20 text-muted-foreground mx-auto mb-6" />
            <h3 className="text-xl font-semibold text-foreground mb-3">No menu items found</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              {searchTerm || Object.values(filters).some(f => f !== 'all' && (typeof f !== 'object' || f.min !== 0 || f.max !== 1000))
                ? 'No items match your current search and filter criteria. Try adjusting your filters or search terms.'
                : 'Get started by adding your first menu item to showcase your delicious Ethiopian cuisine.'
              }
            </p>
            <button
              type="button"
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors shadow-sm"
            >
              <Plus size={20} />
              Add Your First Menu Item
            </button>
          </div>
        )}
      </div>

      {/* Menu Item Form Modal */}
      <MenuItemForm
        item={editingItem}
        categories={categories}
        onSave={handleSaveItem}
        onCancel={handleCloseModal}
        isOpen={showCreateModal || editingItem !== null}
      />
    </div>
  );
}

// Menu Item Card Component
interface MenuItemCardProps {
  item: MenuItem;
  index: number;
  viewMode: 'grid' | 'list';
  onToggleAvailability: () => void;
  onToggleFeatured: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

function MenuItemCard({ 
  item, 
  index, 
  viewMode, 
  onToggleAvailability, 
  onToggleFeatured, 
  onEdit, 
  onDelete 
}: MenuItemCardProps) {
  const [showActions, setShowActions] = useState(false);

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.05 }}
        className="bg-card border rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            {item.image_url && (
              <img
                src={item.image_url}
                alt={item.name}
                className="w-16 h-16 object-cover rounded-lg"
              />
            )}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-foreground">{item.name}</h3>
                {item.is_featured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                {!item.is_available && <EyeOff className="h-4 w-4 text-muted-foreground" />}
              </div>
              <p className="text-sm text-muted-foreground line-clamp-1">{item.description}</p>
              <div className="flex items-center gap-4 mt-1">
                <span className="font-medium text-foreground">{item.price} {item.currency}</span>
                <span className="text-sm text-muted-foreground">{item.category?.name}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={onToggleAvailability}
              className={`p-2 rounded-lg transition-colors ${
                item.is_available
                  ? 'text-green-600 hover:bg-green-50'
                  : 'text-muted-foreground hover:bg-muted'
              }`}
              title={item.is_available ? 'Hide item' : 'Show item'}
            >
              {item.is_available ? <Eye size={16} /> : <EyeOff size={16} />}
            </button>

            <button
              type="button"
              onClick={onToggleFeatured}
              className={`p-2 rounded-lg transition-colors ${
                item.is_featured
                  ? 'text-yellow-500 hover:bg-yellow-50'
                  : 'text-muted-foreground hover:bg-muted'
              }`}
              title={item.is_featured ? 'Remove from featured' : 'Add to featured'}
            >
              <Star size={16} className={item.is_featured ? 'fill-current' : ''} />
            </button>

            <button
              type="button"
              onClick={onEdit}
              className="p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
              title="Edit item"
            >
              <Edit size={16} />
            </button>

            <button
              type="button"
              onClick={onDelete}
              className="p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
              title="Delete item"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow group"
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Image */}
      <div className="relative h-48 bg-muted">
        {item.image_url ? (
          <img
            src={item.image_url}
            alt={item.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <ChefHat className="h-12 w-12 text-muted-foreground" />
          </div>
        )}
        
        {/* Overlay Actions */}
        <div className={`absolute inset-0 bg-black/50 flex items-center justify-center gap-2 transition-opacity ${
          showActions ? 'opacity-100' : 'opacity-0'
        }`}>
          <button
            type="button"
            onClick={onToggleAvailability}
            className={`p-2 rounded-lg bg-white/90 transition-colors ${
              item.is_available ? 'text-green-600' : 'text-muted-foreground'
            }`}
            title={item.is_available ? 'Hide item' : 'Show item'}
          >
            {item.is_available ? <Eye size={16} /> : <EyeOff size={16} />}
          </button>

          <button
            type="button"
            onClick={onToggleFeatured}
            className={`p-2 rounded-lg bg-white/90 transition-colors ${
              item.is_featured ? 'text-yellow-500' : 'text-muted-foreground'
            }`}
            title={item.is_featured ? 'Remove from featured' : 'Add to featured'}
          >
            <Star size={16} className={item.is_featured ? 'fill-current' : ''} />
          </button>

          <button
            type="button"
            onClick={onEdit}
            className="p-2 rounded-lg bg-white/90 text-blue-600 transition-colors"
            title="Edit item"
          >
            <Edit size={16} />
          </button>

          <button
            type="button"
            onClick={onDelete}
            className="p-2 rounded-lg bg-white/90 text-red-600 transition-colors"
            title="Delete item"
          >
            <Trash2 size={16} />
          </button>
        </div>

        {/* Status Badges */}
        <div className="absolute top-2 left-2 flex gap-1">
          {item.is_featured && (
            <span className="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full flex items-center gap-1">
              <Star size={12} className="fill-current" />
              Featured
            </span>
          )}
          {!item.is_available && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">
              Hidden
            </span>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-foreground line-clamp-1">{item.name}</h3>
          <span className="font-bold text-primary">{item.price} {item.currency}</span>
        </div>
        
        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
          {item.description || 'No description available'}
        </p>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{item.category?.name}</span>
          {item.spice_level > 0 && (
            <div className="flex items-center gap-1">
              {Array.from({ length: item.spice_level }).map((_, i) => (
                <div key={i} className="w-2 h-2 bg-red-500 rounded-full" />
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
