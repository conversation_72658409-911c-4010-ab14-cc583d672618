'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Star,
  ChefHat,
  Utensils,
  Grid,
  List,
  MoreVertical
} from 'lucide-react';
import { Category, MenuItem } from '@/lib/supabase';
import { useAuth } from '@/components/auth-provider';
import { MenuItemForm } from '@/components/menu-item-form';

interface MenuStats {
  totalItems: number;
  availableItems: number;
  featuredItems: number;
  totalCategories: number;
}

export default function MenuManagementPage() {
  const { user } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [stats, setStats] = useState<MenuStats>({
    totalItems: 0,
    availableItems: 0,
    featuredItems: 0,
    totalCategories: 0
  });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch categories and menu items
        const [categoriesRes, menuRes] = await Promise.all([
          fetch('/api/categories'),
          fetch('/api/menu')
        ]);

        const categoriesData = await categoriesRes.json();
        const menuData = await menuRes.json();

        setCategories(categoriesData);
        setMenuItems(menuData);
        setFilteredItems(menuData);

        // Calculate stats
        setStats({
          totalItems: menuData.length,
          availableItems: menuData.filter((item: MenuItem) => item.is_available).length,
          featuredItems: menuData.filter((item: MenuItem) => item.is_featured).length,
          totalCategories: categoriesData.length
        });

      } catch (error) {
        console.error('Error fetching menu data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter items based on search and category
  useEffect(() => {
    let filtered = menuItems;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category_id === selectedCategory);
    }

    setFilteredItems(filtered);
  }, [menuItems, searchTerm, selectedCategory]);

  const handleToggleAvailability = async (item: MenuItem) => {
    try {
      const response = await fetch(`/api/menu/${item.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_available: !item.is_available })
      });

      if (response.ok) {
        const updatedItem = await response.json();
        setMenuItems(prev => prev.map(i => i.id === item.id ? updatedItem : i));
      }
    } catch (error) {
      console.error('Error updating item availability:', error);
    }
  };

  const handleToggleFeatured = async (item: MenuItem) => {
    try {
      const response = await fetch(`/api/menu/${item.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_featured: !item.is_featured })
      });

      if (response.ok) {
        const updatedItem = await response.json();
        setMenuItems(prev => prev.map(i => i.id === item.id ? updatedItem : i));
      }
    } catch (error) {
      console.error('Error updating featured status:', error);
    }
  };

  const handleDeleteItem = async (item: MenuItem) => {
    if (!confirm(`Are you sure you want to delete "${item.name}"?`)) return;

    try {
      const response = await fetch(`/api/menu/${item.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setMenuItems(prev => prev.filter(i => i.id !== item.id));
      }
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const handleSaveItem = async (itemData: Partial<MenuItem>) => {
    try {
      const url = editingItem ? `/api/menu/${editingItem.id}` : '/api/menu';
      const method = editingItem ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(itemData)
      });

      if (response.ok) {
        const savedItem = await response.json();

        if (editingItem) {
          setMenuItems(prev => prev.map(i => i.id === editingItem.id ? savedItem : i));
        } else {
          setMenuItems(prev => [...prev, savedItem]);
        }

        setShowCreateModal(false);
        setEditingItem(null);
      }
    } catch (error) {
      console.error('Error saving item:', error);
    }
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingItem(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Menu Management</h1>
          <p className="text-muted-foreground">Manage your restaurant's menu items and categories</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
        >
          <Plus size={20} />
          Add Menu Item
        </motion.button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {[
          { title: 'Total Items', value: stats.totalItems, icon: Utensils, color: 'text-blue-600' },
          { title: 'Available', value: stats.availableItems, icon: Eye, color: 'text-green-600' },
          { title: 'Featured', value: stats.featuredItems, icon: Star, color: 'text-yellow-600' },
          { title: 'Categories', value: stats.totalCategories, icon: ChefHat, color: 'text-purple-600' }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-card p-6 rounded-lg border shadow-sm"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{stat.title}</p>
                <p className="text-2xl font-bold text-foreground">{stat.value}</p>
              </div>
              <stat.icon className={`h-8 w-8 ${stat.color}`} />
            </div>
          </motion.div>
        ))}
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
          <input
            type="text"
            placeholder="Search menu items..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        >
          <option value="all">All Categories</option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>

        <div className="flex items-center gap-2 border border-border rounded-lg p-1">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}`}
          >
            <Grid size={20} />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}`}
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {/* Menu Items */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
        {filteredItems.map((item, index) => (
          <MenuItemCard
            key={item.id}
            item={item}
            index={index}
            viewMode={viewMode}
            onToggleAvailability={() => handleToggleAvailability(item)}
            onToggleFeatured={() => handleToggleFeatured(item)}
            onEdit={() => setEditingItem(item)}
            onDelete={() => handleDeleteItem(item)}
          />
        ))}
      </div>

      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <ChefHat className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No menu items found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedCategory !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'Get started by adding your first menu item.'
            }
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Plus size={20} />
            Add Menu Item
          </button>
        </div>
      )}

      {/* Menu Item Form Modal */}
      <MenuItemForm
        item={editingItem}
        categories={categories}
        onSave={handleSaveItem}
        onCancel={handleCloseModal}
        isOpen={showCreateModal || editingItem !== null}
      />
    </div>
  );
}

// Menu Item Card Component
interface MenuItemCardProps {
  item: MenuItem;
  index: number;
  viewMode: 'grid' | 'list';
  onToggleAvailability: () => void;
  onToggleFeatured: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

function MenuItemCard({ 
  item, 
  index, 
  viewMode, 
  onToggleAvailability, 
  onToggleFeatured, 
  onEdit, 
  onDelete 
}: MenuItemCardProps) {
  const [showActions, setShowActions] = useState(false);

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.05 }}
        className="bg-card border rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            {item.image_url && (
              <img
                src={item.image_url}
                alt={item.name}
                className="w-16 h-16 object-cover rounded-lg"
              />
            )}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-foreground">{item.name}</h3>
                {item.is_featured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                {!item.is_available && <EyeOff className="h-4 w-4 text-muted-foreground" />}
              </div>
              <p className="text-sm text-muted-foreground line-clamp-1">{item.description}</p>
              <div className="flex items-center gap-4 mt-1">
                <span className="font-medium text-foreground">{item.price} {item.currency}</span>
                <span className="text-sm text-muted-foreground">{item.category?.name}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={onToggleAvailability}
              className={`p-2 rounded-lg transition-colors ${
                item.is_available 
                  ? 'text-green-600 hover:bg-green-50' 
                  : 'text-muted-foreground hover:bg-muted'
              }`}
              title={item.is_available ? 'Hide item' : 'Show item'}
            >
              {item.is_available ? <Eye size={16} /> : <EyeOff size={16} />}
            </button>
            
            <button
              onClick={onToggleFeatured}
              className={`p-2 rounded-lg transition-colors ${
                item.is_featured 
                  ? 'text-yellow-500 hover:bg-yellow-50' 
                  : 'text-muted-foreground hover:bg-muted'
              }`}
              title={item.is_featured ? 'Remove from featured' : 'Add to featured'}
            >
              <Star size={16} className={item.is_featured ? 'fill-current' : ''} />
            </button>
            
            <button
              onClick={onEdit}
              className="p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
              title="Edit item"
            >
              <Edit size={16} />
            </button>
            
            <button
              onClick={onDelete}
              className="p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
              title="Delete item"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow group"
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Image */}
      <div className="relative h-48 bg-muted">
        {item.image_url ? (
          <img
            src={item.image_url}
            alt={item.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <ChefHat className="h-12 w-12 text-muted-foreground" />
          </div>
        )}
        
        {/* Overlay Actions */}
        <div className={`absolute inset-0 bg-black/50 flex items-center justify-center gap-2 transition-opacity ${
          showActions ? 'opacity-100' : 'opacity-0'
        }`}>
          <button
            onClick={onToggleAvailability}
            className={`p-2 rounded-lg bg-white/90 transition-colors ${
              item.is_available ? 'text-green-600' : 'text-muted-foreground'
            }`}
            title={item.is_available ? 'Hide item' : 'Show item'}
          >
            {item.is_available ? <Eye size={16} /> : <EyeOff size={16} />}
          </button>
          
          <button
            onClick={onToggleFeatured}
            className={`p-2 rounded-lg bg-white/90 transition-colors ${
              item.is_featured ? 'text-yellow-500' : 'text-muted-foreground'
            }`}
            title={item.is_featured ? 'Remove from featured' : 'Add to featured'}
          >
            <Star size={16} className={item.is_featured ? 'fill-current' : ''} />
          </button>
          
          <button
            onClick={onEdit}
            className="p-2 rounded-lg bg-white/90 text-blue-600 transition-colors"
            title="Edit item"
          >
            <Edit size={16} />
          </button>
          
          <button
            onClick={onDelete}
            className="p-2 rounded-lg bg-white/90 text-red-600 transition-colors"
            title="Delete item"
          >
            <Trash2 size={16} />
          </button>
        </div>

        {/* Status Badges */}
        <div className="absolute top-2 left-2 flex gap-1">
          {item.is_featured && (
            <span className="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full flex items-center gap-1">
              <Star size={12} className="fill-current" />
              Featured
            </span>
          )}
          {!item.is_available && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">
              Hidden
            </span>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-foreground line-clamp-1">{item.name}</h3>
          <span className="font-bold text-primary">{item.price} {item.currency}</span>
        </div>
        
        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
          {item.description || 'No description available'}
        </p>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{item.category?.name}</span>
          {item.spice_level > 0 && (
            <div className="flex items-center gap-1">
              {Array.from({ length: item.spice_level }).map((_, i) => (
                <div key={i} className="w-2 h-2 bg-red-500 rounded-full" />
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
