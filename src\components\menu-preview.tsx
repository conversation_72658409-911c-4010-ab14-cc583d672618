'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';
import { ChefHat, Leaf, Flame, Star } from 'lucide-react';

export function MenuPreview() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [activeCategory, setActiveCategory] = useState('signature');

  const categories = [
    { id: 'signature', name: 'Signature Dishes', icon: Star },
    { id: 'traditional', name: 'Traditional', icon: ChefHat },
    { id: 'vegetarian', name: 'Vegetarian', icon: Leaf },
    { id: 'spicy', name: 'Spicy Favorites', icon: Flame },
  ];

  const menuItems = {
    signature: [
      {
        name: "Chachi's Royal Platter",
        description: "A magnificent combination of our finest dishes served on traditional injera",
        price: "850 ETB",
        highlight: "Chef's Special"
      },
      {
        name: "Ethiopian Coffee Ceremony",
        description: "Traditional coffee ceremony with roasted beans and aromatic spices",
        price: "200 ETB",
        highlight: "Cultural Experience"
      },
      {
        name: "Berbere Spiced Lamb",
        description: "Tender lamb slow-cooked with our signature berbere spice blend",
        price: "650 ETB",
        highlight: "House Favorite"
      }
    ],
    traditional: [
      {
        name: "Doro Wat",
        description: "Ethiopia's national dish - chicken stew with hard-boiled eggs",
        price: "450 ETB",
        highlight: "National Dish"
      },
      {
        name: "Kitfo",
        description: "Ethiopian steak tartare seasoned with mitmita and clarified butter",
        price: "550 ETB",
        highlight: "Traditional"
      },
      {
        name: "Injera Platter",
        description: "Assorted traditional dishes served on fresh injera bread",
        price: "400 ETB",
        highlight: "Authentic"
      }
    ],
    vegetarian: [
      {
        name: "Vegetarian Combination",
        description: "A variety of lentil, chickpea, and vegetable dishes",
        price: "350 ETB",
        highlight: "Plant-Based"
      },
      {
        name: "Shiro Wat",
        description: "Creamy chickpea stew with traditional spices",
        price: "280 ETB",
        highlight: "Comfort Food"
      },
      {
        name: "Gomen",
        description: "Collard greens sautéed with garlic and ginger",
        price: "220 ETB",
        highlight: "Fresh & Healthy"
      }
    ],
    spicy: [
      {
        name: "Siga Wat",
        description: "Fiery beef stew with extra berbere spice",
        price: "500 ETB",
        highlight: "Extra Spicy"
      },
      {
        name: "Minchet Abish",
        description: "Spiced ground beef with jalapeños and herbs",
        price: "420 ETB",
        highlight: "Bold Flavors"
      },
      {
        name: "Awaze Tibs",
        description: "Sautéed meat with spicy awaze sauce",
        price: "480 ETB",
        highlight: "Fire Hot"
      }
    ]
  };

  return (
    <section id="menu" className="py-20 lg:py-32 bg-background" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={isInView ? { scale: 1, rotate: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="inline-block mb-6"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto">
                <ChefHat size={32} className="text-white" />
              </div>
            </motion.div>
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-foreground mb-6">
              Our <span className="text-primary">Culinary</span> Journey
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Discover the rich tapestry of Ethiopian flavors, each dish crafted with love and 
              steeped in centuries of culinary tradition.
            </p>
          </motion.div>

          {/* Category Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {categories.map((category, index) => (
              <motion.button
                key={category.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={isInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeCategory === category.id
                    ? 'bg-primary text-primary-foreground shadow-lg scale-105'
                    : 'bg-muted text-muted-foreground hover:bg-primary/10 hover:text-primary'
                }`}
              >
                <category.icon size={20} />
                <span className="hidden sm:inline">{category.name}</span>
              </motion.button>
            ))}
          </motion.div>

          {/* Menu Items */}
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {menuItems[activeCategory as keyof typeof menuItems].map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-muted/50 rounded-2xl p-6 border border-border hover:border-primary/30 transition-all duration-300 group"
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
                    {item.name}
                  </h3>
                  <span className="text-primary font-bold text-lg">
                    {item.price}
                  </span>
                </div>
                
                <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                  {item.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="inline-block bg-primary/10 text-primary text-xs font-medium px-3 py-1 rounded-full">
                    {item.highlight}
                  </span>
                  
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200"
                  >
                    <Star size={16} />
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="text-center mt-16"
          >
            <p className="text-muted-foreground mb-6">
              Ready to embark on a culinary adventure?
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              View Full Menu
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
