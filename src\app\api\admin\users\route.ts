import { NextRequest, NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// GET /api/admin/users - Get all admin users
export async function GET(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    // Get all admin users from database using admin client
    const { data: adminUsers, error: dbError } = await supabaseAdmin
      .from('admin_users')
      .select('*')
      .order('created_at', { ascending: false });

    if (dbError) {
      throw dbError;
    }

    // Get auth users to merge last sign in data
    const { data: authUsers } = await supabaseAdmin.auth.admin.listUsers();
    
    // Merge data
    const usersWithAuthData = adminUsers.map(adminUser => {
      const authUser = authUsers.users.find(au => au.id === adminUser.id);
      return {
        ...adminUser,
        last_sign_in_at: authUser?.last_sign_in_at || null,
        email_confirmed_at: authUser?.email_confirmed_at || null,
      };
    });

    return NextResponse.json({ users: usersWithAuthData });

  } catch (error) {
    console.error('Error fetching admin users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch admin users' },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create new admin user
export async function POST(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Check if user is authenticated and is IT admin
    const { data: { user }, error: authError2 } = await supabaseServer.auth.getUser();

    if (authError2 || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is IT admin using admin client to bypass RLS
    const { data: adminUser } = await supabaseAdmin
      .from('admin_users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!adminUser || adminUser.role !== 'it_admin') {
      return NextResponse.json({ error: 'Forbidden - IT Admin access required' }, { status: 403 });
    }

    const { email, full_name, role, password } = await request.json();

    // Validate input
    if (!email || !full_name || !role || !password) {
      return NextResponse.json(
        { error: 'Missing required fields: email, full_name, role, password' },
        { status: 400 }
      );
    }

    if (!['it_admin', 'restaurant_admin'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be it_admin or restaurant_admin' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('admin_users')
      .select('email')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Create user in Supabase Auth
    const { data: authUser, error: createUserError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name,
        role,
        is_admin: true,
      }
    });

    if (createUserError) {
      throw createUserError;
    }

    // Create admin user record in database
    const { data: newAdminUser, error: dbError } = await supabase
      .from('admin_users')
      .insert({
        id: authUser.user?.id,
        email,
        full_name,
        role,
        is_active: true,
      })
      .select()
      .single();

    if (dbError) {
      // Clean up auth user if database insert fails
      await supabaseAdmin.auth.admin.deleteUser(authUser.user?.id || '');
      throw dbError;
    }

    console.log(`New admin user created by ${user.email}:`, newAdminUser);

    return NextResponse.json({
      message: 'Admin user created successfully',
      user: newAdminUser
    });

  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create admin user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
