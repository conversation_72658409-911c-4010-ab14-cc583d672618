"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"75683612ae69\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcSm9zc3kgUmVzdGF1cmFudFxcY2hhY2hpc1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzU2ODM2MTJhZTY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\n// Create Supabase client for public operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Create Supabase admin client for server-side operations (bypasses RLS)\n// This should only be used on the server side\nfunction createAdminClient() {\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Only create admin client on server side\nconst supabaseAdmin =  false ? 0 : null;\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(\"\\n        *,\\n        category:categories(*)\\n      \").eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item (using admin client)\n    async create (item) {\n        const { data, error } = await supabaseAdmin.from('menu_items').insert(item).select(\"\\n        *,\\n        category:categories(*)\\n      \").single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item (using admin client)\n    async update (id, updates) {\n        console.log('Updating menu item in database:', id, updates);\n        // First check if the item exists using admin client\n        const { data: existingItem, error: checkError } = await supabaseAdmin.from('menu_items').select('*').eq('id', id).single();\n        console.log('Existing item check:', {\n            existingItem,\n            checkError\n        });\n        if (checkError || !existingItem) {\n            throw new Error(\"Menu item with ID \".concat(id, \" not found: \").concat((checkError === null || checkError === void 0 ? void 0 : checkError.message) || 'No data returned'));\n        }\n        // Update using admin client\n        const { data: updateResult, error: updateError } = await supabaseAdmin.from('menu_items').update(updates).eq('id', id).select(\"\\n        *,\\n        category:categories(*)\\n      \");\n        console.log('Update result:', {\n            updateResult,\n            updateError,\n            rowCount: updateResult === null || updateResult === void 0 ? void 0 : updateResult.length\n        });\n        if (updateError) {\n            console.error('Update error:', updateError);\n            throw updateError;\n        }\n        if (!updateResult || updateResult.length === 0) {\n            throw new Error('No rows were updated - this might be a permissions issue');\n        }\n        if (updateResult.length > 1) {\n            console.warn('Multiple rows updated, returning first one');\n        }\n        return updateResult[0];\n    },\n    // Delete menu item (using admin client)\n    async delete (id) {\n        const { error } = await supabaseAdmin.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});