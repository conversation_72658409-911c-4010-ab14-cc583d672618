import { HeroSection } from "@/components/hero-section";
import { AboutSection } from "@/components/about-section";
import { MenuPreview } from "@/components/menu-preview";
import { LocationSection } from "@/components/location-section";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { ScrollProgress, ParallaxWrapper } from "@/components/animations/scroll-animations";
import { PageTransition } from "@/components/animations/page-transition";
import { SecretMenuEasterEgg, SparkleTrail, CoffeeBeanRain } from "@/components/animations/surprise-elements";
import { PerformanceMonitor, AdaptiveAnimations } from "@/components/animations/performance-monitor";

export default function Home() {
  return (
    <PageTransition>
      <AdaptiveAnimations>
        <main className="min-h-screen">
          <PerformanceMonitor />
          <ScrollProgress />
          <SparkleTrail />
          <CoffeeBeanRain />
          <SecretMenuEasterEgg />
          <Navigation />
        <HeroSection />
        <ParallaxWrapper speed={0.3}>
          <AboutSection />
        </ParallaxWrapper>
        <MenuPreview />
        <ParallaxWrapper speed={0.2}>
          <LocationSection />
        </ParallaxWrapper>
          <Footer />
        </main>
      </AdaptiveAnimations>
    </PageTransition>
  );
}
