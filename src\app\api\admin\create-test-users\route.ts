import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/admin/create-test-users - Create test admin users for development
export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'This endpoint is only available in development' },
        { status: 403 }
      );
    }

    console.log('Creating test admin users...');

    // Test users to create
    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        full_name: 'IT Administrator',
        role: 'it_admin' as const,
      },
      {
        email: '<EMAIL>',
        password: 'admin123',
        full_name: 'Restaurant Manager',
        role: 'restaurant_admin' as const,
      }
    ];

    const results = [];

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const { data: existingUser } = await supabaseAdmin.auth.admin.listUsers();
        const userExists = existingUser.users.some(u => u.email === userData.email);

        if (userExists) {
          console.log(`User ${userData.email} already exists, skipping...`);
          results.push({
            email: userData.email,
            status: 'exists',
            message: 'User already exists'
          });
          continue;
        }

        // Create user in Supabase Auth
        const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true,
          user_metadata: {
            full_name: userData.full_name,
            role: userData.role,
            is_admin: true,
          }
        });

        if (authError) {
          console.error(`Error creating auth user ${userData.email}:`, authError);
          results.push({
            email: userData.email,
            status: 'error',
            message: authError.message
          });
          continue;
        }

        // Create admin user record in database
        const { data: adminUser, error: dbError } = await supabaseAdmin
          .from('admin_users')
          .insert({
            id: authUser.user?.id,
            email: userData.email,
            full_name: userData.full_name,
            role: userData.role,
            is_active: true,
          })
          .select()
          .single();

        if (dbError) {
          console.error(`Error creating admin user record ${userData.email}:`, dbError);
          // Clean up auth user if database insert fails
          await supabaseAdmin.auth.admin.deleteUser(authUser.user?.id || '');
          results.push({
            email: userData.email,
            status: 'error',
            message: dbError.message
          });
          continue;
        }

        console.log(`Successfully created user: ${userData.email}`);
        results.push({
          email: userData.email,
          status: 'created',
          message: 'User created successfully',
          user: {
            id: adminUser.id,
            email: adminUser.email,
            full_name: adminUser.full_name,
            role: adminUser.role
          }
        });

      } catch (error) {
        console.error(`Error processing user ${userData.email}:`, error);
        results.push({
          email: userData.email,
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      message: 'Test user creation completed',
      results,
      credentials: {
        'IT Admin': {
          email: '<EMAIL>',
          password: 'admin123',
          access: 'Full system access including analytics, settings, user management'
        },
        'Restaurant Admin': {
          email: '<EMAIL>',
          password: 'admin123',
          access: 'Menu management and restaurant information only'
        }
      }
    });

  } catch (error) {
    console.error('Error in create-test-users API:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create test users', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/create-test-users - Get test user info
export async function GET() {
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  return NextResponse.json({
    message: 'Test admin users for development',
    credentials: {
      'IT Admin': {
        email: '<EMAIL>',
        password: 'admin123',
        access: 'Full system access including analytics, settings, user management'
      },
      'Restaurant Admin': {
        email: '<EMAIL>',
        password: 'admin123',
        access: 'Menu management and restaurant information only'
      }
    },
    note: 'Use POST request to create these users if they don\'t exist'
  });
}
