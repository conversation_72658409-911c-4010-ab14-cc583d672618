import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create Supabase client for public operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Create Supabase admin client for server-side operations (bypasses RLS)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Database types
export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MenuItem {
  id: string;
  category_id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  image_url?: string;
  is_available: boolean;
  is_featured: boolean;
  spice_level: number;
  dietary_info?: Record<string, any>;
  ingredients?: string[];
  allergens?: string[];
  preparation_time?: number;
  display_order: number;
  created_at: string;
  updated_at: string;
  category?: Category;
}

export interface AdminUser {
  id: string;
  email: string;
  full_name: string;
  role: 'it_admin' | 'restaurant_admin';
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface RestaurantInfo {
  id: string;
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  opening_hours?: Record<string, any>;
  social_media?: Record<string, any>;
  settings?: Record<string, any>;
  updated_at: string;
  updated_by?: string;
}

// API functions for categories
export const categoryAPI = {
  // Get all active categories
  async getAll(): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get category by ID
  async getById(id: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new category
  async create(category: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .insert(category)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update category
  async update(id: string, updates: Partial<Category>): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete category
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
};

// API functions for menu items
export const menuAPI = {
  // Get all available menu items with categories
  async getAll(): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('is_available', true)
      .order('display_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get menu items by category
  async getByCategory(categoryId: string): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('category_id', categoryId)
      .eq('is_available', true)
      .order('display_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get featured menu items
  async getFeatured(): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('is_featured', true)
      .eq('is_available', true)
      .order('display_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get menu item by ID
  async getById(id: string): Promise<MenuItem | null> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new menu item (using admin client)
  async create(item: Omit<MenuItem, 'id' | 'created_at' | 'updated_at' | 'category'>): Promise<MenuItem> {
    const { data, error } = await supabaseAdmin
      .from('menu_items')
      .insert(item)
      .select(`
        *,
        category:categories(*)
      `)
      .single();

    if (error) throw error;
    return data;
  },

  // Update menu item (using admin client)
  async update(id: string, updates: Partial<MenuItem>): Promise<MenuItem> {
    console.log('Updating menu item in database:', id, updates);

    // First check if the item exists using admin client
    const { data: existingItem, error: checkError } = await supabaseAdmin
      .from('menu_items')
      .select('*')
      .eq('id', id)
      .single();

    console.log('Existing item check:', { existingItem, checkError });

    if (checkError || !existingItem) {
      throw new Error(`Menu item with ID ${id} not found: ${checkError?.message || 'No data returned'}`);
    }

    // Update using admin client
    const { data: updateResult, error: updateError } = await supabaseAdmin
      .from('menu_items')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        category:categories(*)
      `);

    console.log('Update result:', { updateResult, updateError, rowCount: updateResult?.length });

    if (updateError) {
      console.error('Update error:', updateError);
      throw updateError;
    }

    if (!updateResult || updateResult.length === 0) {
      throw new Error('No rows were updated - this might be a permissions issue');
    }

    if (updateResult.length > 1) {
      console.warn('Multiple rows updated, returning first one');
    }

    return updateResult[0];
  },

  // Delete menu item (using admin client)
  async delete(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('menu_items')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
};

// API functions for restaurant info
export const restaurantAPI = {
  // Get restaurant information
  async getInfo(): Promise<RestaurantInfo | null> {
    const { data, error } = await supabase
      .from('restaurant_info')
      .select('*')
      .limit(1)
      .single();

    if (error) throw error;
    return data;
  },

  // Update restaurant information
  async updateInfo(updates: Partial<RestaurantInfo>): Promise<RestaurantInfo> {
    const { data, error } = await supabase
      .from('restaurant_info')
      .update(updates)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// API functions for admin users
export const adminAPI = {
  // Get all admin users
  async getAll(): Promise<AdminUser[]> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Get admin user by email
  async getByEmail(email: string): Promise<AdminUser | null> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new admin user
  async create(user: Omit<AdminUser, 'id' | 'created_at' | 'updated_at'>): Promise<AdminUser> {
    const { data, error } = await supabase
      .from('admin_users')
      .insert(user)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update admin user
  async update(id: string, updates: Partial<AdminUser>): Promise<AdminUser> {
    const { data, error } = await supabase
      .from('admin_users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};
