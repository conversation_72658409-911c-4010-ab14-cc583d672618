'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import { Heart, Star, Sparkles, Coffee, Crown } from 'lucide-react';

// Confetti explosion effect
interface ConfettiProps {
  trigger: boolean;
  onComplete?: () => void;
}

export function Confetti({ trigger, onComplete }: ConfettiProps) {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    color: string;
    rotation: number;
    scale: number;
  }>>([]);

  useEffect(() => {
    if (trigger) {
      const newParticles = Array.from({ length: 50 }, (_, i) => ({
        id: i,
        x: Math.random() * window.innerWidth,
        y: window.innerHeight + 100,
        color: ['#d4af37', '#8b4513', '#dc143c', '#f4d03f', '#ff6b6b'][Math.floor(Math.random() * 5)],
        rotation: Math.random() * 360,
        scale: Math.random() * 0.5 + 0.5,
      }));

      setParticles(newParticles);

      setTimeout(() => {
        setParticles([]);
        onComplete?.();
      }, 3000);
    }
  }, [trigger, onComplete]);

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute w-3 h-3 rounded-full"
            style={{
              backgroundColor: particle.color,
              left: particle.x,
              top: particle.y,
            }}
            initial={{
              y: particle.y,
              x: particle.x,
              rotate: particle.rotation,
              scale: particle.scale,
              opacity: 1,
            }}
            animate={{
              y: particle.y - window.innerHeight - 200,
              x: particle.x + (Math.random() - 0.5) * 200,
              rotate: particle.rotation + 720,
              opacity: 0,
            }}
            transition={{
              duration: 3,
              ease: 'easeOut',
            }}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

// Floating hearts effect
interface FloatingHeartsProps {
  trigger: boolean;
  count?: number;
}

export function FloatingHearts({ trigger, count = 10 }: FloatingHeartsProps) {
  const [hearts, setHearts] = useState<Array<{
    id: number;
    x: number;
    delay: number;
  }>>([]);

  useEffect(() => {
    if (trigger) {
      const newHearts = Array.from({ length: count }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        delay: Math.random() * 2,
      }));

      setHearts(newHearts);

      setTimeout(() => {
        setHearts([]);
      }, 4000);
    }
  }, [trigger, count]);

  return (
    <div className="fixed inset-0 pointer-events-none z-40">
      <AnimatePresence>
        {hearts.map((heart) => (
          <motion.div
            key={heart.id}
            className="absolute bottom-0 text-red-500"
            style={{ left: `${heart.x}%` }}
            initial={{ y: 0, opacity: 0, scale: 0 }}
            animate={{ 
              y: -window.innerHeight, 
              opacity: [0, 1, 1, 0], 
              scale: [0, 1, 1, 0],
              x: [0, (Math.random() - 0.5) * 100, (Math.random() - 0.5) * 200]
            }}
            transition={{
              duration: 4,
              delay: heart.delay,
              ease: 'easeOut',
            }}
          >
            <Heart size={24} fill="currentColor" />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

// Secret menu easter egg
export function SecretMenuEasterEgg() {
  const [isRevealed, setIsRevealed] = useState(false);
  const [clickCount, setClickCount] = useState(0);
  const clickTimeouts = useRef<NodeJS.Timeout[]>([]);

  const handleSecretClick = () => {
    setClickCount(prev => prev + 1);
    
    // Clear existing timeouts
    clickTimeouts.current.forEach(timeout => clearTimeout(timeout));
    clickTimeouts.current = [];

    // Reset click count after 3 seconds
    const timeout = setTimeout(() => {
      setClickCount(0);
    }, 3000);
    clickTimeouts.current.push(timeout);

    // Reveal secret menu after 7 clicks
    if (clickCount >= 6) {
      setIsRevealed(true);
      setClickCount(0);
    }
  };

  return (
    <>
      {/* Hidden trigger */}
      <div
        className="fixed bottom-4 right-4 w-8 h-8 opacity-0 cursor-pointer z-50"
        onClick={handleSecretClick}
      />

      {/* Secret menu modal */}
      <AnimatePresence>
        {isRevealed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsRevealed(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, rotateY: -90 }}
              animate={{ scale: 1, opacity: 1, rotateY: 0 }}
              exit={{ scale: 0.8, opacity: 0, rotateY: 90 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
              className="bg-gradient-to-br from-primary/20 to-accent/20 backdrop-blur-md border border-primary/30 rounded-2xl p-8 max-w-md w-full text-center"
              onClick={(e) => e.stopPropagation()}
            >
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 0.5, repeat: 2 }}
              >
                <Crown className="w-16 h-16 text-primary mx-auto mb-4" />
              </motion.div>
              
              <h3 className="text-2xl font-serif font-bold text-primary mb-4">
                🎉 Secret Menu Unlocked! 🎉
              </h3>
              
              <p className="text-foreground mb-6">
                You've discovered our hidden Ethiopian coffee ceremony experience! 
                Ask your server about our traditional coffee ritual.
              </p>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsRevealed(false)}
                className="bg-primary text-primary-foreground px-6 py-3 rounded-full font-semibold"
              >
                Amazing! ✨
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Click progress indicator */}
      {clickCount > 0 && clickCount < 7 && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          className="fixed bottom-16 right-4 bg-primary/20 backdrop-blur-sm border border-primary/30 rounded-full p-2 z-40"
        >
          <div className="flex gap-1">
            {Array.from({ length: 7 }).map((_, i) => (
              <motion.div
                key={i}
                className={`w-2 h-2 rounded-full ${
                  i < clickCount ? 'bg-primary' : 'bg-primary/30'
                }`}
                animate={i < clickCount ? { scale: [1, 1.3, 1] } : {}}
                transition={{ duration: 0.3 }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </>
  );
}

// Sparkle trail cursor effect
export function SparkleTrail() {
  const [sparkles, setSparkles] = useState<Array<{
    id: number;
    x: number;
    y: number;
  }>>([]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (Math.random() > 0.7) { // Only create sparkles 30% of the time
        const newSparkle = {
          id: Date.now() + Math.random(),
          x: e.clientX,
          y: e.clientY,
        };

        setSparkles(prev => [...prev, newSparkle]);

        setTimeout(() => {
          setSparkles(prev => prev.filter(s => s.id !== newSparkle.id));
        }, 1000);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-30">
      <AnimatePresence>
        {sparkles.map((sparkle) => (
          <motion.div
            key={sparkle.id}
            className="absolute text-primary"
            style={{ left: sparkle.x - 12, top: sparkle.y - 12 }}
            initial={{ opacity: 0, scale: 0, rotate: 0 }}
            animate={{ 
              opacity: [0, 1, 0], 
              scale: [0, 1, 0], 
              rotate: 180,
              y: -20
            }}
            transition={{ duration: 1, ease: 'easeOut' }}
          >
            <Sparkles size={24} />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

// Ethiopian coffee bean animation
export function CoffeeBeanRain() {
  const [beans, setBeans] = useState<Array<{
    id: number;
    x: number;
    delay: number;
    duration: number;
  }>>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.8) { // 20% chance every interval
        const newBean = {
          id: Date.now() + Math.random(),
          x: Math.random() * 100,
          delay: 0,
          duration: Math.random() * 3 + 2,
        };

        setBeans(prev => [...prev, newBean]);

        setTimeout(() => {
          setBeans(prev => prev.filter(b => b.id !== newBean.id));
        }, newBean.duration * 1000);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-20">
      <AnimatePresence>
        {beans.map((bean) => (
          <motion.div
            key={bean.id}
            className="absolute text-secondary/30"
            style={{ left: `${bean.x}%`, top: -50 }}
            initial={{ y: -50, rotate: 0, opacity: 0 }}
            animate={{ 
              y: window.innerHeight + 50, 
              rotate: 360,
              opacity: [0, 0.7, 0]
            }}
            transition={{
              duration: bean.duration,
              ease: 'linear',
            }}
          >
            <Coffee size={16} />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
