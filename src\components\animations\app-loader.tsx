'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState } from 'react';

interface AppLoaderProps {
  onLoadingComplete?: () => void;
  duration?: number;
}

export function AppLoader({ onLoadingComplete, duration = 3000 }: AppLoaderProps) {
  const [loadingStage, setLoadingStage] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const stages = [
      { delay: 0, stage: 0 },
      { delay: 800, stage: 1 },
      { delay: 1600, stage: 2 },
      { delay: 2400, stage: 3 },
      { delay: duration, stage: 4 }
    ];

    const timeouts = stages.map(({ delay, stage }) =>
      setTimeout(() => {
        setLoadingStage(stage);
        if (stage === 4) {
          setTimeout(() => {
            setIsVisible(false);
            onLoadingComplete?.();
          }, 500);
        }
      }, delay)
    );

    return () => timeouts.forEach(clearTimeout);
  }, [duration, onLoadingComplete]);

  const loadingMessages = [
    "Welcome to Chachi's",
    "Preparing Ethiopian flavors...",
    "Setting up your experience...",
    "Almost ready!"
  ];

  const coffeeBeansVariants = {
    initial: { scale: 0, rotate: 0 },
    animate: { 
      scale: [0, 1.2, 1], 
      rotate: [0, 180, 360],
      transition: { 
        duration: 1.5, 
        ease: "easeInOut",
        repeat: Infinity,
        repeatDelay: 0.5
      }
    }
  };

  const textVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: { duration: 0.4 }
    }
  };

  const progressVariants = {
    initial: { width: "0%" },
    animate: { 
      width: `${(loadingStage / 3) * 100}%`,
      transition: { duration: 0.8, ease: "easeInOut" }
    }
  };

  const containerVariants = {
    initial: { opacity: 1 },
    exit: { 
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.5, ease: "easeInOut" }
    }
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        variants={containerVariants}
        initial="initial"
        exit="exit"
        className="fixed inset-0 z-50 bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 dark:from-amber-950 dark:via-orange-950 dark:to-red-950 flex items-center justify-center"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000000" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div className="relative z-10 text-center">
          {/* Coffee Bean Animation */}
          <div className="mb-8 flex justify-center">
            <div className="relative">
              {/* Main coffee bean */}
              <motion.div
                variants={coffeeBeansVariants}
                initial="initial"
                animate="animate"
                className="w-16 h-16 bg-gradient-to-br from-amber-700 to-amber-900 rounded-full relative"
              >
                {/* Coffee bean crack */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-8 bg-amber-100 rounded-full rotate-12"></div>
              </motion.div>

              {/* Floating particles */}
              {Array.from({ length: 6 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-amber-600 rounded-full"
                  initial={{ 
                    x: 0, 
                    y: 0, 
                    opacity: 0,
                    scale: 0
                  }}
                  animate={{
                    x: Math.cos((i * 60) * Math.PI / 180) * 40,
                    y: Math.sin((i * 60) * Math.PI / 180) * 40,
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0],
                    transition: {
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: "easeInOut"
                    }
                  }}
                  style={{
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)'
                  }}
                />
              ))}
            </div>
          </div>

          {/* Loading Text */}
          <div className="mb-8 h-8">
            <AnimatePresence mode="wait">
              {loadingStage < 4 && (
                <motion.h2
                  key={loadingStage}
                  variants={textVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  className="text-2xl font-bold text-amber-900 dark:text-amber-100"
                >
                  {loadingMessages[loadingStage]}
                </motion.h2>
              )}
            </AnimatePresence>
          </div>

          {/* Progress Bar */}
          <div className="w-64 mx-auto">
            <div className="h-2 bg-amber-200 dark:bg-amber-800 rounded-full overflow-hidden">
              <motion.div
                variants={progressVariants}
                initial="initial"
                animate="animate"
                className="h-full bg-gradient-to-r from-amber-500 to-orange-500 rounded-full"
              />
            </div>
            
            {/* Progress Percentage */}
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-sm text-amber-700 dark:text-amber-300 mt-2"
            >
              {Math.round((loadingStage / 3) * 100)}%
            </motion.p>
          </div>

          {/* Ethiopian Pattern Decoration */}
          <div className="mt-8 flex justify-center space-x-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <motion.div
                key={i}
                className="w-3 h-3 bg-amber-600 dark:bg-amber-400 rounded-full"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 1,
                  transition: { 
                    delay: i * 0.1,
                    duration: 0.5,
                    ease: "easeOut"
                  }
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// Hook to manage app loading state
export function useAppLoader(autoStart = true) {
  const [isLoading, setIsLoading] = useState(autoStart);
  const [hasLoaded, setHasLoaded] = useState(false);

  const startLoading = () => {
    setIsLoading(true);
    setHasLoaded(false);
  };

  const completeLoading = () => {
    setIsLoading(false);
    setHasLoaded(true);
  };

  return {
    isLoading,
    hasLoaded,
    startLoading,
    completeLoading
  };
}
