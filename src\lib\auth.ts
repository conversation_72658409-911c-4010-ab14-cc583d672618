import { createBrowserClient } from '@supabase/ssr';
import { supabase } from './supabase';

// User role types
export type UserRole = 'it_admin' | 'restaurant_admin' | 'user';

// User interface
export interface User {
  id: string;
  email: string;
  full_name?: string;
  role: UserRole;
  is_admin: boolean;
  last_login?: string;
}

// Auth utilities for client components
export const createClientAuth = () => {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  
  return {
    // Sign in with email and password
    async signIn(email: string, password: string) {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) throw error;
      return data;
    },

    // Sign out
    async signOut() {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },

    // Get current session
    async getSession() {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      return session;
    },

    // Get current user with role information
    async getCurrentUser(): Promise<User | null> {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) return null;

      const metadata = user.user_metadata || {};
      return {
        id: user.id,
        email: user.email!,
        full_name: metadata.full_name,
        role: metadata.role || 'user',
        is_admin: metadata.is_admin || false,
        last_login: metadata.last_login,
      };
    },

    // Listen to auth state changes
    onAuthStateChange(callback: (user: User | null) => void) {
      return supabase.auth.onAuthStateChange(async (event, session) => {
        if (session?.user) {
          const metadata = session.user.user_metadata || {};
          const user: User = {
            id: session.user.id,
            email: session.user.email!,
            full_name: metadata.full_name,
            role: metadata.role || 'user',
            is_admin: metadata.is_admin || false,
            last_login: metadata.last_login,
          };
          callback(user);
        } else {
          callback(null);
        }
      });
    },
  };
};

// Server auth utilities are in auth-server.ts to avoid client-side imports

// Role checking utilities
export const roleUtils = {
  // Check if user is IT admin
  isITAdmin(user: User | null): boolean {
    return user?.role === 'it_admin' && user?.is_admin === true;
  },

  // Check if user is restaurant admin
  isRestaurantAdmin(user: User | null): boolean {
    return user?.role === 'restaurant_admin' && user?.is_admin === true;
  },

  // Check if user is any kind of admin
  isAdmin(user: User | null): boolean {
    return user?.is_admin === true;
  },

  // Check if user can manage menu items
  canManageMenu(user: User | null): boolean {
    return this.isITAdmin(user) || this.isRestaurantAdmin(user);
  },

  // Check if user can manage system settings
  canManageSystem(user: User | null): boolean {
    return this.isITAdmin(user);
  },

  // Check if user can manage other admins
  canManageAdmins(user: User | null): boolean {
    return this.isITAdmin(user);
  },

  // Get role display name
  getRoleDisplayName(role: UserRole): string {
    switch (role) {
      case 'it_admin':
        return 'IT Administrator';
      case 'restaurant_admin':
        return 'Restaurant Administrator';
      default:
        return 'User';
    }
  },
};

// Admin user management functions
export const adminAPI = {
  // Create admin user (IT admin only)
  async createAdminUser(userData: {
    email: string;
    full_name: string;
    role: 'it_admin' | 'restaurant_admin';
    password: string;
  }) {
    // First create the admin record in our database
    const { data: adminUser, error: dbError } = await supabase
      .from('admin_users')
      .insert({
        email: userData.email,
        full_name: userData.full_name,
        role: userData.role,
        is_active: true,
      })
      .select()
      .single();

    if (dbError) throw dbError;

    // Then invite the user via Supabase Auth
    const { data, error } = await supabase.auth.admin.inviteUserByEmail(userData.email, {
      data: {
        full_name: userData.full_name,
        role: userData.role,
        is_admin: true,
      },
    });

    if (error) {
      // If auth invitation fails, clean up the database record
      await supabase.from('admin_users').delete().eq('id', adminUser.id);
      throw error;
    }

    return { adminUser, authUser: data.user };
  },

  // Update admin user status
  async updateAdminStatus(adminId: string, isActive: boolean) {
    const { data, error } = await supabase
      .from('admin_users')
      .update({ is_active: isActive, updated_at: new Date().toISOString() })
      .eq('id', adminId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get all admin users
  async getAllAdmins() {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },
};

// Password reset utilities
export const passwordUtils = {
  // Send password reset email
  async sendPasswordReset(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    
    if (error) throw error;
  },

  // Update password
  async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });
    
    if (error) throw error;
  },
};
