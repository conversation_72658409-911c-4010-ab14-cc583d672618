import { supabase } from './supabase';

export async function seedDatabase() {
  try {
    console.log('Starting database seeding...');

    // First, let's check if categories exist, if not create them
    const { data: existingCategories } = await supabase
      .from('categories')
      .select('*');

    if (!existingCategories || existingCategories.length === 0) {
      console.log('Creating categories...');
      const { data: categories, error: categoriesError } = await supabase
        .from('categories')
        .insert([
          {
            name: 'Appetizers',
            description: 'Start your meal with these delicious appetizers',
            display_order: 1,
            is_active: true
          },
          {
            name: 'Main Dishes',
            description: 'Traditional Ethiopian main courses',
            display_order: 2,
            is_active: true
          },
          {
            name: 'Vegetarian',
            description: 'Delicious vegetarian options',
            display_order: 3,
            is_active: true
          },
          {
            name: 'Beverages',
            description: 'Traditional and modern drinks',
            display_order: 4,
            is_active: true
          }
        ])
        .select();

      if (categoriesError) {
        console.error('Error creating categories:', categoriesError);
        throw categoriesError;
      }
      console.log('Categories created:', categories);
    } else {
      console.log('Categories already exist:', existingCategories);
    }

    // Get categories for menu items
    const { data: categories } = await supabase
      .from('categories')
      .select('*')
      .order('display_order');

    if (!categories || categories.length === 0) {
      throw new Error('No categories found');
    }

    // Check if menu items exist
    const { data: existingMenuItems } = await supabase
      .from('menu_items')
      .select('*');

    if (!existingMenuItems || existingMenuItems.length === 0) {
      console.log('Creating menu items...');
      
      const appetizersCategory = categories.find(c => c.name === 'Appetizers');
      const mainDishesCategory = categories.find(c => c.name === 'Main Dishes');
      const vegetarianCategory = categories.find(c => c.name === 'Vegetarian');
      const beveragesCategory = categories.find(c => c.name === 'Beverages');

      const menuItems = [
        // Appetizers
        {
          name: 'Sambusa',
          description: 'Crispy pastry filled with lentils and spices',
          price: 8.99,
          currency: 'USD',
          category_id: appetizersCategory?.id,
          is_available: true,
          is_featured: true,
          spice_level: 2,
          dietary_info: ['vegetarian'],
          ingredients: ['lentils', 'pastry', 'onions', 'spices'],
          preparation_time: 15,
          display_order: 1
        },
        {
          name: 'Timatim Salad',
          description: 'Fresh tomato and onion salad with Ethiopian spices',
          price: 6.99,
          currency: 'USD',
          category_id: appetizersCategory?.id,
          is_available: true,
          is_featured: false,
          spice_level: 1,
          dietary_info: ['vegetarian', 'vegan'],
          ingredients: ['tomatoes', 'onions', 'jalapeños', 'olive oil'],
          preparation_time: 10,
          display_order: 2
        },
        // Main Dishes
        {
          name: 'Doro Wot',
          description: 'Traditional Ethiopian chicken stew with berbere spice',
          price: 18.99,
          currency: 'USD',
          category_id: mainDishesCategory?.id,
          is_available: true,
          is_featured: true,
          spice_level: 4,
          dietary_info: [],
          ingredients: ['chicken', 'berbere spice', 'onions', 'eggs'],
          preparation_time: 45,
          display_order: 1
        },
        {
          name: 'Kitfo',
          description: 'Ethiopian steak tartare seasoned with mitmita spice',
          price: 22.99,
          currency: 'USD',
          category_id: mainDishesCategory?.id,
          is_available: true,
          is_featured: true,
          spice_level: 3,
          dietary_info: [],
          ingredients: ['beef', 'mitmita spice', 'clarified butter'],
          preparation_time: 20,
          display_order: 2
        },
        // Vegetarian
        {
          name: 'Shiro',
          description: 'Ground chickpea stew with Ethiopian spices',
          price: 14.99,
          currency: 'USD',
          category_id: vegetarianCategory?.id,
          is_available: true,
          is_featured: true,
          spice_level: 3,
          dietary_info: ['vegetarian', 'vegan'],
          ingredients: ['chickpeas', 'berbere spice', 'onions', 'garlic'],
          preparation_time: 30,
          display_order: 1
        },
        {
          name: 'Gomen',
          description: 'Collard greens cooked with garlic and ginger',
          price: 12.99,
          currency: 'USD',
          category_id: vegetarianCategory?.id,
          is_available: true,
          is_featured: false,
          spice_level: 2,
          dietary_info: ['vegetarian', 'vegan'],
          ingredients: ['collard greens', 'garlic', 'ginger', 'onions'],
          preparation_time: 25,
          display_order: 2
        },
        // Beverages
        {
          name: 'Ethiopian Coffee',
          description: 'Traditional Ethiopian coffee ceremony',
          price: 4.99,
          currency: 'USD',
          category_id: beveragesCategory?.id,
          is_available: true,
          is_featured: true,
          spice_level: 0,
          dietary_info: ['vegetarian', 'vegan'],
          ingredients: ['Ethiopian coffee beans'],
          preparation_time: 15,
          display_order: 1
        },
        {
          name: 'Tej',
          description: 'Traditional Ethiopian honey wine',
          price: 8.99,
          currency: 'USD',
          category_id: beveragesCategory?.id,
          is_available: true,
          is_featured: false,
          spice_level: 0,
          dietary_info: ['vegetarian'],
          ingredients: ['honey', 'water', 'hops'],
          preparation_time: 5,
          display_order: 2
        }
      ];

      const { data: createdMenuItems, error: menuError } = await supabase
        .from('menu_items')
        .insert(menuItems)
        .select();

      if (menuError) {
        console.error('Error creating menu items:', menuError);
        throw menuError;
      }
      
      console.log('Menu items created:', createdMenuItems);
    } else {
      console.log('Menu items already exist:', existingMenuItems.length, 'items');
    }

    console.log('Database seeding completed successfully!');
    return { success: true };
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

// Function to clear all data (for testing)
export async function clearDatabase() {
  try {
    console.log('Clearing database...');
    
    // Delete menu items first (due to foreign key constraints)
    await supabase.from('menu_items').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    // Delete categories
    await supabase.from('categories').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    console.log('Database cleared successfully!');
  } catch (error) {
    console.error('Error clearing database:', error);
    throw error;
  }
}
