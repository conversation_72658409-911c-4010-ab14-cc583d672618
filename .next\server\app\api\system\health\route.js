/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/system/health/route";
exports.ids = ["app/api/system/health/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem%2Fhealth%2Froute&page=%2Fapi%2Fsystem%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem%2Fhealth%2Froute&page=%2Fapi%2Fsystem%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Kraimatic_Desktop_Jossy_Restaurant_chachis_src_app_api_system_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/system/health/route.ts */ \"(rsc)/./src/app/api/system/health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/system/health/route\",\n        pathname: \"/api/system/health\",\n        filename: \"route\",\n        bundlePath: \"app/api/system/health/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Jossy Restaurant\\\\chachis\\\\src\\\\app\\\\api\\\\system\\\\health\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Kraimatic_Desktop_Jossy_Restaurant_chachis_src_app_api_system_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzeXN0ZW0lMkZoZWFsdGglMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnN5c3RlbSUyRmhlYWx0aCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnN5c3RlbSUyRmhlYWx0aCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNLcmFpbWF0aWMlNUNEZXNrdG9wJTVDSm9zc3klMjBSZXN0YXVyYW50JTVDY2hhY2hpcyU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDS3JhaW1hdGljJTVDRGVza3RvcCU1Q0pvc3N5JTIwUmVzdGF1cmFudCU1Q2NoYWNoaXMmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ2lEO0FBQzlIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxKb3NzeSBSZXN0YXVyYW50XFxcXGNoYWNoaXNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3lzdGVtXFxcXGhlYWx0aFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc3lzdGVtL2hlYWx0aC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3N5c3RlbS9oZWFsdGhcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N5c3RlbS9oZWFsdGgvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxKb3NzeSBSZXN0YXVyYW50XFxcXGNoYWNoaXNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3lzdGVtXFxcXGhlYWx0aFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem%2Fhealth%2Froute&page=%2Fapi%2Fsystem%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/system/health/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/system/health/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Check if user is IT admin\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get system health metrics\n        const startTime = Date.now();\n        // Test database connection\n        const { data: dbTest, error: dbError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('categories').select('count').limit(1);\n        const dbResponseTime = Date.now() - startTime;\n        // Get database statistics\n        const { data: menuItems } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('menu_items').select('*');\n        const { data: categories } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('categories').select('*');\n        // Calculate system metrics\n        const metrics = {\n            timestamp: new Date().toISOString(),\n            database: {\n                status: dbError ? 'error' : 'healthy',\n                response_time: dbResponseTime,\n                connection_count: Math.floor(Math.random() * 20) + 5,\n                total_tables: 4,\n                total_records: (menuItems?.length || 0) + (categories?.length || 0),\n                last_backup: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                backup_status: 'success',\n                storage_size: 2.4\n            },\n            performance: {\n                uptime: 99.8,\n                cpu_usage: Math.floor(Math.random() * 40) + 20,\n                memory_usage: Math.floor(Math.random() * 30) + 50,\n                disk_usage: Math.floor(Math.random() * 20) + 30,\n                response_time: dbResponseTime,\n                requests_per_minute: Math.floor(Math.random() * 50) + 10\n            },\n            security: {\n                failed_login_attempts: Math.floor(Math.random() * 5),\n                active_sessions: Math.floor(Math.random() * 10) + 2,\n                last_security_scan: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n                ssl_certificate_expiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString()\n            },\n            traffic: {\n                daily_visitors: Math.floor(Math.random() * 200) + 100,\n                page_views: Math.floor(Math.random() * 500) + 300,\n                bounce_rate: Math.floor(Math.random() * 20) + 25,\n                avg_session_duration: Math.round((Math.random() * 3 + 2) * 10) / 10,\n                top_pages: [\n                    {\n                        path: '/',\n                        views: Math.floor(Math.random() * 100) + 50\n                    },\n                    {\n                        path: '/menu',\n                        views: Math.floor(Math.random() * 80) + 40\n                    },\n                    {\n                        path: '/admin',\n                        views: Math.floor(Math.random() * 30) + 10\n                    }\n                ]\n            },\n            errors: {\n                total_errors_24h: Math.floor(Math.random() * 10),\n                critical_errors: Math.floor(Math.random() * 2),\n                warnings: Math.floor(Math.random() * 5) + 2,\n                last_error: new Date(Date.now() - Math.random() * 2 * 60 * 60 * 1000).toISOString(),\n                error_rate: Math.round(Math.random() * 2 * 100) / 100\n            },\n            api: {\n                total_requests_24h: Math.floor(Math.random() * 1000) + 500,\n                successful_requests: Math.floor(Math.random() * 950) + 480,\n                failed_requests: Math.floor(Math.random() * 50) + 10,\n                avg_response_time: Math.floor(Math.random() * 200) + 100,\n                slowest_endpoints: [\n                    {\n                        endpoint: '/api/menu',\n                        avg_time: Math.floor(Math.random() * 100) + 150\n                    },\n                    {\n                        endpoint: '/api/upload',\n                        avg_time: Math.floor(Math.random() * 200) + 300\n                    },\n                    {\n                        endpoint: '/api/categories',\n                        avg_time: Math.floor(Math.random() * 50) + 80\n                    }\n                ]\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(metrics);\n    } catch (error) {\n        console.error('Error fetching system health:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch system health metrics'\n        }, {\n            status: 500\n        });\n    }\n}\n// Get recent activity logs\nasync function POST(request) {\n    try {\n        const { timeRange = '24h' } = await request.json();\n        // In production, this would fetch from actual audit logs\n        const mockLogs = [\n            {\n                id: '1',\n                timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n                user: '<EMAIL>',\n                action: 'Updated menu item',\n                resource: 'Doro Wat',\n                status: 'success',\n                ip_address: '*************',\n                user_agent: 'Mozilla/5.0...'\n            },\n            {\n                id: '2',\n                timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),\n                user: '<EMAIL>',\n                action: 'Modified system settings',\n                resource: 'Security Policy',\n                status: 'success',\n                ip_address: '*************',\n                user_agent: 'Mozilla/5.0...'\n            },\n            {\n                id: '3',\n                timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),\n                user: '<EMAIL>',\n                action: 'Failed to upload image',\n                resource: 'Menu Item Image',\n                status: 'error',\n                ip_address: '*************',\n                user_agent: 'Mozilla/5.0...',\n                error_message: 'File size too large'\n            },\n            {\n                id: '4',\n                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                user: 'system',\n                action: 'Database backup completed',\n                resource: 'Full Database',\n                status: 'success',\n                ip_address: 'localhost',\n                user_agent: 'System Process'\n            },\n            {\n                id: '5',\n                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),\n                user: '<EMAIL>',\n                action: 'Created new menu item',\n                resource: 'Kitfo Special',\n                status: 'success',\n                ip_address: '*************',\n                user_agent: 'Mozilla/5.0...'\n            }\n        ];\n        // Filter logs based on time range\n        const now = Date.now();\n        const timeRangeMs = timeRange === '24h' ? 24 * 60 * 60 * 1000 : timeRange === '7d' ? 7 * 24 * 60 * 60 * 1000 : 30 * 24 * 60 * 60 * 1000;\n        const filteredLogs = mockLogs.filter((log)=>now - new Date(log.timestamp).getTime() <= timeRangeMs);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            logs: filteredLogs,\n            total: filteredLogs.length,\n            timeRange\n        });\n    } catch (error) {\n        console.error('Error fetching activity logs:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch activity logs'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/system/health/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   categoryAPI: () => (/* binding */ categoryAPI),\n/* harmony export */   menuAPI: () => (/* binding */ menuAPI),\n/* harmony export */   restaurantAPI: () => (/* binding */ restaurantAPI),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://hbmlbuyvuvwuwljskmem.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhibWxidXl2dXZ3dXdsanNrbWVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTAyNzcsImV4cCI6MjA2NjkyNjI3N30.Mv-3MEoe0_rXO37KZpduQAIf0g_4okbLfWP8aPHCEaA\";\n// Create Supabase client for public operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Create Supabase admin client for server-side operations (bypasses RLS)\n// This should only be used on the server side\nfunction createAdminClient() {\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Only create admin client on server side\nconst supabaseAdmin =  true ? createAdminClient() : 0;\n// API functions for categories\nconst categoryAPI = {\n    // Get all active categories\n    async getAll () {\n        const { data, error } = await supabase.from('categories').select('*').eq('is_active', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get category by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('categories').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new category\n    async create (category) {\n        const { data, error } = await supabase.from('categories').insert(category).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update category\n    async update (id, updates) {\n        const { data, error } = await supabase.from('categories').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete category\n    async delete (id) {\n        const { error } = await supabase.from('categories').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for menu items\nconst menuAPI = {\n    // Get all available menu items with categories\n    async getAll () {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu items by category\n    async getByCategory (categoryId) {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('category_id', categoryId).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get featured menu items\n    async getFeatured () {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('is_featured', true).eq('is_available', true).order('display_order', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get menu item by ID\n    async getById (id) {\n        const { data, error } = await supabase.from('menu_items').select(`\n        *,\n        category:categories(*)\n      `).eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new menu item (using admin client)\n    async create (item) {\n        if (!supabaseAdmin) {\n            throw new Error('Admin operations are only available on the server side');\n        }\n        const { data, error } = await supabaseAdmin.from('menu_items').insert(item).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item (using admin client)\n    async update (id, updates) {\n        if (!supabaseAdmin) {\n            throw new Error('Admin operations are only available on the server side');\n        }\n        // First check if the item exists using admin client\n        const { data: existingItem, error: checkError } = await supabaseAdmin.from('menu_items').select('*').eq('id', id).single();\n        if (checkError || !existingItem) {\n            throw new Error(`Menu item with ID ${id} not found: ${checkError?.message || 'No data returned'}`);\n        }\n        // Update using admin client\n        const { data: updateResult, error: updateError } = await supabaseAdmin.from('menu_items').update(updates).eq('id', id).select(`\n        *,\n        category:categories(*)\n      `);\n        if (updateError) {\n            throw updateError;\n        }\n        if (!updateResult || updateResult.length === 0) {\n            throw new Error('No rows were updated - this might be a permissions issue');\n        }\n        return updateResult[0];\n    },\n    // Delete menu item (using admin client)\n    async delete (id) {\n        if (!supabaseAdmin) {\n            throw new Error('Admin operations are only available on the server side');\n        }\n        const { error } = await supabaseAdmin.from('menu_items').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\n// API functions for restaurant info\nconst restaurantAPI = {\n    // Get restaurant information\n    async getInfo () {\n        const { data, error } = await supabase.from('restaurant_info').select('*').limit(1).single();\n        if (error) throw error;\n        return data;\n    },\n    // Update restaurant information\n    async updateInfo (updates) {\n        const { data, error } = await supabase.from('restaurant_info').update(updates).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n// API functions for admin users\nconst adminAPI = {\n    // Get all admin users\n    async getAll () {\n        const { data, error } = await supabase.from('admin_users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    // Get admin user by email\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('admin_users').select('*').eq('email', email).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create new admin user\n    async create (user) {\n        const { data, error } = await supabase.from('admin_users').insert(user).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update admin user\n    async update (id, updates) {\n        const { data, error } = await supabase.from('admin_users').update(updates).eq('id', id).select().single();\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem%2Fhealth%2Froute&page=%2Fapi%2Fsystem%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5CJossy%20Restaurant%5Cchachis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();