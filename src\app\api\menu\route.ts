import { NextRequest, NextResponse } from 'next/server';
import { menuAPI } from '@/lib/supabase';

// GET /api/menu - Get all menu items or filter by category
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('category');
    const featured = searchParams.get('featured');

    let menuItems;

    if (featured === 'true') {
      menuItems = await menuAPI.getFeatured();
    } else if (categoryId) {
      menuItems = await menuAPI.getByCategory(categoryId);
    } else {
      menuItems = await menuAPI.getAll();
    }

    return NextResponse.json(menuItems);
  } catch (error) {
    console.error('Error fetching menu items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch menu items' },
      { status: 500 }
    );
  }
}

// POST /api/menu - Create new menu item (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.category_id || !body.price) {
      return NextResponse.json(
        { error: 'Name, category_id, and price are required' },
        { status: 400 }
      );
    }

    const menuItem = await menuAPI.create({
      category_id: body.category_id,
      name: body.name,
      description: body.description || null,
      price: parseFloat(body.price),
      currency: body.currency || 'ETB',
      image_url: body.image_url || null,
      is_available: body.is_available !== undefined ? body.is_available : true,
      is_featured: body.is_featured !== undefined ? body.is_featured : false,
      spice_level: body.spice_level || 0,
      dietary_info: body.dietary_info || null,
      ingredients: body.ingredients || null,
      allergens: body.allergens || null,
      preparation_time: body.preparation_time || null,
      display_order: body.display_order || 0,
    });

    return NextResponse.json(menuItem, { status: 201 });
  } catch (error) {
    console.error('Error creating menu item:', error);
    return NextResponse.json(
      { error: 'Failed to create menu item' },
      { status: 500 }
    );
  }
}
