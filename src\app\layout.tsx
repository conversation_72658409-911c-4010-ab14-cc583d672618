import type { Metada<PERSON> } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/components/auth-provider";
import { Toaster } from "sonner";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON>'s Restaurant - Authentic Ethiopian Cuisine in Addis Ababa",
  description: "Experience the finest Ethiopian cuisine at Cha<PERSON>'s Restaurant in Addis Ababa. Discover traditional flavors, elegant ambiance, and exceptional hospitality.",
  keywords: ["Ethiopian restaurant", "Addis Ababa", "traditional cuisine", "fine dining", "Ethiopian food"],
  authors: [{ name: "<PERSON><PERSON>'s Restaurant" }],
  openGraph: {
    title: "<PERSON><PERSON>'s Restaurant - Authentic Ethiopian Cuisine",
    description: "Experience the finest Ethiopian cuisine in Addis Ababa",
    type: "website",
    locale: "en_US",
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#fefefe' },
    { media: '(prefers-color-scheme: dark)', color: '#0f0f0f' }
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${playfair.variable} antialiased`}
      >
        <ThemeProvider>
          <AuthProvider>
            {children}
            <Toaster
              position="top-right"
              expand={true}
              richColors
              closeButton
            />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
