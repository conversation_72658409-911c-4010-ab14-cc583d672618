import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Chachi's Restaurant - Authentic Ethiopian Cuisine in Addis Ababa",
  description: "Experience the finest Ethiopian cuisine at Cha<PERSON>'s Restaurant in Addis Ababa. Discover traditional flavors, elegant ambiance, and exceptional hospitality.",
  keywords: ["Ethiopian restaurant", "Addis Ababa", "traditional cuisine", "fine dining", "Ethiopian food"],
  authors: [{ name: "Chachi's Restaurant" }],
  openGraph: {
    title: "Chachi's Restaurant - Authentic Ethiopian Cuisine",
    description: "Experience the finest Ethiopian cuisine in Addis Ababa",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${playfair.variable} antialiased`}
      >
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
